import api from './index'

// 用户相关类型定义
export interface User {
  id: number
  username: string
  email?: string
  role: 'admin' | 'user'
}

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  password: string
  email?: string
  role?: 'admin' | 'user'
}

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

export interface LoginResponse {
  token: string
  user: User
}

// 认证API
export const authApi = {
  // 用户登录
  login(data: LoginForm): Promise<ApiResponse<LoginResponse>> {
    return api.post('/auth/login', data)
  },

  // 用户注册
  register(data: RegisterForm): Promise<ApiResponse<User>> {
    return api.post('/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser(): Promise<ApiResponse<User>> {
    return api.get('/auth/me')
  }
}
