import api from './index'
import type { ApiResponse } from './auth'

// 客户相关类型定义
export interface Customer {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  credit_limit?: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface CustomerForm {
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  credit_limit?: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface CustomerQuery {
  page?: number
  limit?: number
  search?: string
}

// 客户API
export const customerApi = {
  // 获取客户列表
  getCustomers(params?: CustomerQuery): Promise<ApiResponse<PaginatedResponse<Customer>>> {
    return api.get('/customers', { params })
  },

  // 获取单个客户
  getCustomer(id: number): Promise<ApiResponse<Customer>> {
    return api.get(`/customers/${id}`)
  },

  // 创建客户
  createCustomer(data: CustomerForm): Promise<ApiResponse> {
    return api.post('/customers', data)
  },

  // 更新客户
  updateCustomer(id: number, data: Partial<CustomerForm>): Promise<ApiResponse> {
    return api.put(`/customers/${id}`, data)
  },

  // 删除客户
  deleteCustomer(id: number): Promise<ApiResponse> {
    return api.delete(`/customers/${id}`)
  }
}
