import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    // 统一错误处理
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    } else if (error.response?.status === 403) {
      // 权限不足
      console.error('权限不足')
    } else if (error.response?.status >= 500) {
      // 服务器错误
      console.error('服务器错误:', error.response?.data?.message || '服务器内部错误')
    } else if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNREFUSED') {
      // 网络错误
      console.error('网络连接失败，请检查网络连接')
    }

    return Promise.reject(error.response?.data || error.message)
  }
)

export default api
