import api from './index'
import type { ApiResponse } from './auth'

// 库存相关类型定义
export interface InventoryItem {
  id: number
  code: string
  name: string
  specification?: string
  unit: string
  current_stock: number
  stock_min: number
  stock_max: number
  alert_status: 'normal' | 'low_stock' | 'high_stock' | 'zero_stock'
  item_type: 'material' | 'product'
  cost_price?: number
  sale_price?: number
}

export interface InventoryMovement {
  id: number
  item_type: 'material' | 'product'
  item_id: number
  movement_type: 'in' | 'out' | 'adjust'
  quantity: number
  before_quantity: number
  after_quantity: number
  reference_type?: string
  reference_id?: number
  reference_no?: string
  remark?: string
  created_at: string
  created_by?: number
  created_by_name?: string
}

export interface InventoryAlert {
  id: number
  item_type: 'material' | 'product'
  item_id: number
  alert_type: 'low_stock' | 'high_stock' | 'zero_stock'
  current_stock: number
  threshold_value?: number
  status: 'active' | 'resolved'
  created_at: string
  resolved_at?: string
  resolved_by?: number
  item_code?: string
  item_name?: string
  item_unit?: string
}

export interface InventoryQuery {
  page?: number
  limit?: number
  search?: string
  item_type?: 'material' | 'product'
  alert_status?: 'normal' | 'low_stock' | 'high_stock' | 'zero_stock'
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// API 函数
export const inventoryApi = {
  // 获取库存列表（原材料和成品）
  getInventoryList: (params?: InventoryQuery): Promise<ApiResponse<PaginatedResponse<InventoryItem>>> =>
    api.get('/inventory', { params }),

  // 获取原材料库存列表
  getMaterialsInventory: (params?: InventoryQuery): Promise<ApiResponse<PaginatedResponse<InventoryItem>>> =>
    api.get('/inventory/materials', { params }),

  // 获取成品库存列表
  getProductsInventory: (params?: InventoryQuery): Promise<ApiResponse<PaginatedResponse<InventoryItem>>> =>
    api.get('/inventory/products', { params }),

  // 获取库存变动历史
  getInventoryHistory: (itemType: 'material' | 'product', itemId: number, params?: { page?: number; limit?: number }): Promise<ApiResponse<PaginatedResponse<InventoryMovement>>> =>
    api.get(`/inventory/${itemType}/${itemId}/history`, { params }),

  // 获取库存预警信息
  getInventoryAlerts: (params?: { page?: number; limit?: number; status?: 'active' | 'resolved' }): Promise<ApiResponse<PaginatedResponse<InventoryAlert>>> =>
    api.get('/inventory/alerts', { params })
}

export default inventoryApi
