import api from './index'
import type { ApiResponse } from './auth'

// 原材料相关类型定义
export interface Material {
  id: number
  code: string
  name: string
  specification?: string
  unit: string
  cost_price: number
  stock_min: number
  stock_max: number
  current_stock: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface MaterialForm {
  code: string
  name: string
  specification?: string
  unit: string
  cost_price?: number
  stock_min?: number
  stock_max?: number
  current_stock?: number
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface MaterialQuery {
  page?: number
  limit?: number
  search?: string
}

// 原材料API
export const materialApi = {
  // 获取原材料列表
  getMaterials(params?: MaterialQuery): Promise<ApiResponse<PaginatedResponse<Material>>> {
    return api.get('/materials', { params })
  },

  // 获取单个原材料
  getMaterial(id: number): Promise<ApiResponse<Material>> {
    return api.get(`/materials/${id}`)
  },

  // 创建原材料
  createMaterial(data: MaterialForm): Promise<ApiResponse> {
    return api.post('/materials', data)
  },

  // 更新原材料
  updateMaterial(id: number, data: Partial<MaterialForm>): Promise<ApiResponse> {
    return api.put(`/materials/${id}`, data)
  },

  // 删除原材料
  deleteMaterial(id: number): Promise<ApiResponse> {
    return api.delete(`/materials/${id}`)
  }
}
