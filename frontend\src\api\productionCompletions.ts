import api from './index'
import type { ApiResponse } from './auth'

// 生产完工相关类型定义
export interface ProductionCompletion {
  id: number
  completion_no: string
  production_plan_id: number
  production_plan_no?: string
  product_id: number
  product_code?: string
  product_name?: string
  completed_quantity: number
  completion_date: string
  status: 'draft' | 'confirmed'
  remark?: string
  created_at: string
  updated_at: string
  items?: ProductionCompletionItem[]
}

export interface ProductionCompletionItem {
  id: number
  production_completion_id: number
  material_id: number
  material_code?: string
  material_name?: string
  unit?: string
  consumed_quantity: number
  created_at: string
  updated_at: string
}

export interface ProductionCompletionForm {
  production_plan_id: number
  completed_quantity: number
  completion_date: string
  remark?: string
  materials: {
    material_id: number
    consumed_quantity: number
  }[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ProductionCompletionQuery {
  page?: number
  limit?: number
  search?: string
  status?: string
}

// 生产完工API
export const productionCompletionApi = {
  // 获取生产完工单列表
  getProductionCompletions(params?: ProductionCompletionQuery): Promise<ApiResponse<PaginatedResponse<ProductionCompletion>>> {
    return api.get('/production-completions', { params })
  },

  // 获取单个生产完工单
  getProductionCompletion(id: number): Promise<ApiResponse<ProductionCompletion>> {
    return api.get(`/production-completions/${id}`)
  },

  // 创建生产完工单
  createProductionCompletion(data: ProductionCompletionForm): Promise<ApiResponse> {
    return api.post('/production-completions', data)
  }
}
