import api from './index'
import type { ApiResponse } from './auth'

// 生产计划相关类型定义
export interface ProductionPlan {
  id: number
  plan_no: string
  product_id: number
  product_code?: string
  product_name?: string
  planned_quantity: number
  actual_quantity?: number
  plan_date: string
  start_date?: string
  completion_date?: string
  status: 'draft' | 'approved' | 'in_progress' | 'completed' | 'cancelled'
  remark?: string
  created_at: string
  updated_at: string
  items?: ProductionPlanItem[]
}

export interface ProductionPlanItem {
  id: number
  production_plan_id: number
  material_id: number
  material_code?: string
  material_name?: string
  unit?: string
  required_quantity: number
  consumed_quantity?: number
  created_at: string
  updated_at: string
}

export interface ProductionPlanForm {
  product_id: number
  planned_quantity: number
  plan_date: string
  remark?: string
  materials: {
    material_id: number
    required_quantity: number
  }[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ProductionPlanQuery {
  page?: number
  limit?: number
  search?: string
  status?: string
}

// 生产计划API
export const productionPlanApi = {
  // 获取生产计划列表
  getProductionPlans(params?: ProductionPlanQuery): Promise<ApiResponse<PaginatedResponse<ProductionPlan>>> {
    return api.get('/production-plans', { params })
  },

  // 获取单个生产计划
  getProductionPlan(id: number): Promise<ApiResponse<ProductionPlan>> {
    return api.get(`/production-plans/${id}`)
  },

  // 创建生产计划
  createProductionPlan(data: ProductionPlanForm): Promise<ApiResponse> {
    return api.post('/production-plans', data)
  },

  // 审核生产计划
  approveProductionPlan(id: number): Promise<ApiResponse> {
    return api.post(`/production-plans/${id}/approve`)
  },

  // 开始生产
  startProduction(id: number): Promise<ApiResponse> {
    return api.post(`/production-plans/${id}/start`)
  },

  // 更新生产计划状态
  updateProductionPlanStatus(id: number, status: string): Promise<ApiResponse> {
    return api.put(`/production-plans/${id}/status`, { status })
  },

  // 删除生产计划
  deleteProductionPlan(id: number): Promise<ApiResponse> {
    return api.delete(`/production-plans/${id}`)
  }
}
