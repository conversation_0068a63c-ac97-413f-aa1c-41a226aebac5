import api from './index'
import type { ApiResponse } from './auth'

// 成品相关类型定义
export interface Product {
  id: number
  code: string
  name: string
  specification?: string
  unit: string
  cost_price: number
  sale_price: number
  stock_min: number
  stock_max: number
  current_stock: number
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface ProductForm {
  code: string
  name: string
  specification?: string
  unit: string
  cost_price: number
  sale_price: number
  stock_min: number
  stock_max: number
  current_stock?: number
  status?: 'active' | 'inactive'
}

export interface ProductQuery {
  page?: number
  pageSize?: number
  search?: string
  status?: 'active' | 'inactive'
}

export interface ProductListResponse {
  products: Product[]
  total: number
  page: number
  pageSize: number
}

// 成品管理API
export const productApi = {
  // 获取成品列表
  async getProducts(params: ProductQuery = {}): Promise<ProductListResponse> {
    const response = await api.get<any>('/products', { params })
    return response.data
  },

  // 获取单个成品详情
  async getProductById(id: number): Promise<Product> {
    const response = await api.get<ApiResponse<Product>>(`/products/${id}`)
    return response.data
  },

  // 创建成品
  async createProduct(data: ProductForm): Promise<{ id: number }> {
    const response = await api.post<ApiResponse<{ id: number }>>('/products', data)
    return response.data
  },

  // 更新成品
  async updateProduct(id: number, data: ProductForm): Promise<void> {
    await api.put(`/products/${id}`, data)
  },

  // 删除成品
  async deleteProduct(id: number): Promise<void> {
    await api.delete(`/products/${id}`)
  }
}
