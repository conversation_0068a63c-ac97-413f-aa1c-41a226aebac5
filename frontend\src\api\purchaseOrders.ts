import api from './index'
import type { ApiResponse } from './auth'

// 采购订单相关类型定义
export interface PurchaseOrder {
  id: number
  order_no: string
  supplier_id: number
  supplier_name?: string
  order_date: string
  expected_date?: string
  total_amount: number
  status: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled'
  remark?: string
  created_at: string
  updated_at: string
  items?: PurchaseOrderItem[]
}

export interface PurchaseOrderItem {
  id: number
  purchase_order_id: number
  material_id: number
  material_code?: string
  material_name?: string
  unit?: string
  quantity: number
  unit_price: number
  total_price: number
  received_quantity?: number
  created_at: string
  updated_at: string
}

export interface PurchaseOrderForm {
  supplier_id: number
  order_date: string
  expected_date?: string
  remark?: string
  items: {
    material_id: number
    quantity: number
    unit_price: number
  }[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface PurchaseOrderQuery {
  page?: number
  limit?: number
  search?: string
  status?: string
}

// 采购订单API
export const purchaseOrderApi = {
  // 获取采购订单列表
  getPurchaseOrders(params?: PurchaseOrderQuery): Promise<ApiResponse<PaginatedResponse<PurchaseOrder>>> {
    return api.get('/purchase-orders', { params })
  },

  // 获取单个采购订单
  getPurchaseOrder(id: number): Promise<ApiResponse<PurchaseOrder>> {
    return api.get(`/purchase-orders/${id}`)
  },

  // 创建采购订单
  createPurchaseOrder(data: PurchaseOrderForm): Promise<ApiResponse> {
    return api.post('/purchase-orders', data)
  },

  // 审核采购订单
  approvePurchaseOrder(id: number): Promise<ApiResponse> {
    return api.post(`/purchase-orders/${id}/approve`)
  },

  // 更新采购订单状态
  updatePurchaseOrderStatus(id: number, status: string): Promise<ApiResponse> {
    return api.put(`/purchase-orders/${id}/status`, { status })
  },

  // 删除采购订单
  deletePurchaseOrder(id: number): Promise<ApiResponse> {
    return api.delete(`/purchase-orders/${id}`)
  }
}
