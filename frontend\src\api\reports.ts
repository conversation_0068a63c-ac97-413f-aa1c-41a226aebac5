import api from './index'
import type { ApiResponse } from './auth'

// 报表相关类型定义
export interface ReportQuery {
  start_date?: string
  end_date?: string
  item_type?: 'material' | 'product'
  item_id?: number
}

export interface PurchaseSummaryReport {
  period: string
  total_orders: number
  total_amount: number
  total_quantity: number
  supplier_count: number
  items: {
    material_id: number
    material_code: string
    material_name: string
    quantity: number
    amount: number
    unit: string
  }[]
}

export interface SalesSummaryReport {
  period: string
  total_orders: number
  total_amount: number
  total_quantity: number
  customer_count: number
  items: {
    product_id: number
    product_code: string
    product_name: string
    quantity: number
    amount: number
    unit: string
  }[]
}

export interface InventoryMovementReport {
  period: string
  total_movements: number
  in_movements: number
  out_movements: number
  adjust_movements: number
  items: {
    item_id: number
    item_code: string
    item_name: string
    item_type: 'material' | 'product'
    in_quantity: number
    out_quantity: number
    adjust_quantity: number
    net_change: number
    unit: string
  }[]
}

export interface ProductionCostReport {
  period: string
  total_productions: number
  total_cost: number
  total_output_value: number
  profit_margin: number
  items: {
    product_id: number
    product_code: string
    product_name: string
    produced_quantity: number
    material_cost: number
    output_value: number
    unit_cost: number
    unit: string
  }[]
}

export interface FinancialSummaryReport {
  period: string
  purchase_amount: number
  sales_amount: number
  production_cost: number
  gross_profit: number
  profit_margin: number
  inventory_value: number
  monthly_data: {
    month: string
    purchase_amount: number
    sales_amount: number
    profit: number
  }[]
}

// API 函数
export const reportsApi = {
  // 获取采购汇总报表
  getPurchaseSummaryReport: (params: ReportQuery): Promise<ApiResponse<PurchaseSummaryReport>> =>
    api.get('/reports/purchase-summary', { params }),

  // 获取销售汇总报表
  getSalesSummaryReport: (params: ReportQuery): Promise<ApiResponse<SalesSummaryReport>> =>
    api.get('/reports/sales-summary', { params }),

  // 获取库存变动报表
  getInventoryMovementReport: (params: ReportQuery): Promise<ApiResponse<InventoryMovementReport>> =>
    api.get('/reports/inventory-movement', { params }),

  // 获取生产成本报表
  getProductionCostReport: (params: ReportQuery): Promise<ApiResponse<ProductionCostReport>> =>
    api.get('/reports/production-cost', { params }),

  // 获取财务汇总报表
  getFinancialSummaryReport: (params: ReportQuery): Promise<ApiResponse<FinancialSummaryReport>> =>
    api.get('/reports/financial-summary', { params })
}

export default reportsApi
