import api from './index'
import type { ApiResponse } from './auth'

// 销售出库单相关类型定义
export interface SalesDelivery {
  id: number
  delivery_no: string
  sales_order_id: number
  sales_order_no?: string
  customer_id: number
  customer_name?: string
  delivery_date: string
  total_amount: number
  status: 'draft' | 'confirmed'
  remark?: string
  created_at: string
  updated_at: string
  items?: SalesDeliveryItem[]
}

export interface SalesDeliveryItem {
  id: number
  sales_delivery_id: number
  product_id: number
  product_code?: string
  product_name?: string
  unit?: string
  quantity: number
  unit_price: number
  total_price: number
  created_at: string
  updated_at: string
}

export interface SalesDeliveryForm {
  sales_order_id: number
  delivery_date: string
  remark?: string
  items: {
    product_id: number
    quantity: number
    unit_price: number
  }[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface SalesDeliveryQuery {
  page?: number
  limit?: number
  search?: string
  status?: string
}

// 销售出库单API
export const salesDeliveryApi = {
  // 获取销售出库单列表
  getSalesDeliveries(params?: SalesDeliveryQuery): Promise<ApiResponse<PaginatedResponse<SalesDelivery>>> {
    return api.get('/sales-deliveries', { params })
  },

  // 获取单个销售出库单
  getSalesDelivery(id: number): Promise<ApiResponse<SalesDelivery>> {
    return api.get(`/sales-deliveries/${id}`)
  },

  // 创建销售出库单
  createSalesDelivery(data: SalesDeliveryForm): Promise<ApiResponse> {
    return api.post('/sales-deliveries', data)
  }
}
