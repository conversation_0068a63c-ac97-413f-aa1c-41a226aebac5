import api from './index'
import type { ApiResponse } from './auth'

// 销售订单相关类型定义
export interface SalesOrder {
  id: number
  order_no: string
  customer_id: number
  customer_name?: string
  order_date: string
  delivery_date?: string
  total_amount: number
  status: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled'
  remark?: string
  created_at: string
  updated_at: string
  items?: SalesOrderItem[]
}

export interface SalesOrderItem {
  id: number
  sales_order_id: number
  product_id: number
  product_code?: string
  product_name?: string
  unit?: string
  quantity: number
  unit_price: number
  total_price: number
  delivered_quantity?: number
  created_at: string
  updated_at: string
}

export interface SalesOrderForm {
  customer_id: number
  order_date: string
  delivery_date?: string
  remark?: string
  items: {
    product_id: number
    quantity: number
    unit_price: number
  }[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface SalesOrderQuery {
  page?: number
  limit?: number
  search?: string
  status?: string
}

// 销售订单API
export const salesOrderApi = {
  // 获取销售订单列表
  getSalesOrders(params?: SalesOrderQuery): Promise<ApiResponse<PaginatedResponse<SalesOrder>>> {
    return api.get('/sales-orders', { params })
  },

  // 获取单个销售订单
  getSalesOrder(id: number): Promise<ApiResponse<SalesOrder>> {
    return api.get(`/sales-orders/${id}`)
  },

  // 创建销售订单
  createSalesOrder(data: SalesOrderForm): Promise<ApiResponse> {
    return api.post('/sales-orders', data)
  },

  // 审核销售订单
  approveSalesOrder(id: number): Promise<ApiResponse> {
    return api.post(`/sales-orders/${id}/approve`)
  },

  // 更新销售订单状态
  updateSalesOrderStatus(id: number, status: string): Promise<ApiResponse> {
    return api.put(`/sales-orders/${id}/status`, { status })
  },

  // 删除销售订单
  deleteSalesOrder(id: number): Promise<ApiResponse> {
    return api.delete(`/sales-orders/${id}`)
  }
}
