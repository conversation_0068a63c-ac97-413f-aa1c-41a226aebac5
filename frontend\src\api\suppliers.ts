import api from './index'
import type { ApiResponse } from './auth'

// 供应商相关类型定义
export interface Supplier {
  id: number
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  settlement_method?: string
  status: 'active' | 'inactive'
  created_at: string
  updated_at: string
}

export interface SupplierForm {
  code: string
  name: string
  contact_person?: string
  phone?: string
  address?: string
  settlement_method?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface SupplierQuery {
  page?: number
  limit?: number
  search?: string
}

// 供应商API
export const supplierApi = {
  // 获取供应商列表
  getSuppliers(params?: SupplierQuery): Promise<ApiResponse<PaginatedResponse<Supplier>>> {
    return api.get('/suppliers', { params })
  },

  // 获取单个供应商
  getSupplier(id: number): Promise<ApiResponse<Supplier>> {
    return api.get(`/suppliers/${id}`)
  },

  // 创建供应商
  createSupplier(data: SupplierForm): Promise<ApiResponse> {
    return api.post('/suppliers', data)
  },

  // 更新供应商
  updateSupplier(id: number, data: Partial<SupplierForm>): Promise<ApiResponse> {
    return api.put(`/suppliers/${id}`, data)
  },

  // 删除供应商
  deleteSupplier(id: number): Promise<ApiResponse> {
    return api.delete(`/suppliers/${id}`)
  }
}
