<template>
  <div class="data-table">
    <!-- 搜索栏 -->
    <div v-if="showSearch" class="search-bar">
      <el-input
        v-model="searchQuery"
        :placeholder="searchPlaceholder"
        style="width: 300px"
        clearable
        @input="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="data"
      style="width: 100%"
      stripe
      v-bind="$attrs"
    >
      <!-- 根据columns配置动态生成列 -->
      <template v-if="columns && columns.length > 0">
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :sortable="column.sortable"
          :align="column.align || 'left'"
        >
          <template v-if="column.slot" #default="scope">
            <slot :name="column.slot" :row="scope.row" :column="column" :$index="scope.$index" />
          </template>
        </el-table-column>
      </template>

      <!-- 如果没有columns配置，使用传统的slot方式 -->
      <template v-else>
        <slot />
      </template>
    </el-table>

    <!-- 分页 -->
    <div v-if="showPagination" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'

interface Column {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  fixed?: boolean | string
  sortable?: boolean
  align?: 'left' | 'center' | 'right'
  slot?: string
}

interface Props {
  data: any[]
  columns?: Column[]
  loading?: boolean
  total?: number
  showSearch?: boolean
  showPagination?: boolean
  searchPlaceholder?: string
  pageSizes?: number[]
}

interface Emits {
  (e: 'search', query: string): void
  (e: 'page-change', page: number, size: number): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  total: 0,
  showSearch: true,
  showPagination: true,
  searchPlaceholder: '请输入搜索关键词',
  pageSizes: () => [10, 20, 50, 100]
})

const emit = defineEmits<Emits>()

const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索处理
function handleSearch() {
  currentPage.value = 1
  emit('search', searchQuery.value)
}

// 分页处理
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  emit('page-change', currentPage.value, pageSize.value)
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  emit('page-change', currentPage.value, pageSize.value)
}

// 监听外部数据变化，重置分页
watch(() => props.total, () => {
  if (currentPage.value > Math.ceil(props.total / pageSize.value)) {
    currentPage.value = 1
  }
})
</script>

<style scoped>
.data-table {
  width: 100%;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
