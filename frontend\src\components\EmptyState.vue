<template>
  <div class="empty-state">
    <el-icon class="empty-icon" :size="iconSize" :color="iconColor">
      <component :is="icon" />
    </el-icon>
    <h3 class="empty-title">{{ title }}</h3>
    <p class="empty-description">{{ description }}</p>
    <div v-if="$slots.action" class="empty-action">
      <slot name="action" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Box } from '@element-plus/icons-vue'

interface Props {
  icon?: any
  iconSize?: number
  iconColor?: string
  title?: string
  description?: string
}

withDefaults(defineProps<Props>(), {
  icon: Box,
  iconSize: 80,
  iconColor: '#ccc',
  title: '暂无数据',
  description: '当前没有任何数据'
})
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-title {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 18px;
  font-weight: 500;
}

.empty-description {
  margin: 0 0 20px 0;
  color: #999;
  font-size: 14px;
}

.empty-action {
  margin-top: 10px;
}
</style>
