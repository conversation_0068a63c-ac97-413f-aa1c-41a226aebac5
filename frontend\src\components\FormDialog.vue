<template>
  <el-dialog
    v-model="visible"
    :title="title"
    :width="width"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-width="labelWidth"
    >
      <slot :form="formData" />
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ loading ? '保存中...' : '保存' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
  title: string
  formData: Record<string, any>
  rules?: FormRules
  loading?: boolean
  width?: string
  labelWidth?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', formData: Record<string, any>): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  width: '600px',
  labelWidth: '100px'
})

const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const visible = ref(props.modelValue)

// 监听外部visible变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

// 监听内部visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  emit('submit', props.formData)
}

// 关闭对话框
function handleClose() {
  visible.value = false
  emit('close')
}

// 重置表单
function resetForm() {
  formRef.value?.resetFields()
}

// 暴露方法给父组件
defineExpose({
  resetForm
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
