import { ref } from 'vue'

// 全局加载状态管理
const globalLoading = ref(false)
const loadingCount = ref(0)

export function useLoading() {
  const loading = ref(false)

  const startLoading = () => {
    loading.value = true
    loadingCount.value++
    globalLoading.value = true
  }

  const stopLoading = () => {
    loading.value = false
    loadingCount.value = Math.max(0, loadingCount.value - 1)
    if (loadingCount.value === 0) {
      globalLoading.value = false
    }
  }

  const withLoading = async <T>(fn: () => Promise<T>): Promise<T> => {
    startLoading()
    try {
      return await fn()
    } finally {
      stopLoading()
    }
  }

  return {
    loading,
    globalLoading,
    startLoading,
    stopLoading,
    withLoading
  }
}

export default useLoading
