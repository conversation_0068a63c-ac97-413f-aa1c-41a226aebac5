import { ref } from 'vue'
import { defineStore } from 'pinia'
import { authApi, type User, type LoginForm } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const isLoggedIn = ref(!!token.value)

  // 登录
  async function login(loginForm: LoginForm) {
    try {
      const response = await authApi.login(loginForm)
      if (response.success && response.data) {
        token.value = response.data.token
        user.value = response.data.user
        isLoggedIn.value = true

        // 保存到本地存储
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(response.data.user))

        return { success: true, message: response.message }
      } else {
        return { success: false, message: response.message || '登录失败' }
      }
    } catch (error: any) {
      console.error('登录错误:', error)
      return { success: false, message: error.message || '登录失败' }
    }
  }

  // 退出登录
  function logout() {
    user.value = null
    token.value = null
    isLoggedIn.value = false

    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 初始化用户信息
  function initAuth() {
    const savedUser = localStorage.getItem('user')
    const savedToken = localStorage.getItem('token')

    if (savedUser && savedToken) {
      try {
        user.value = JSON.parse(savedUser)
        token.value = savedToken
        isLoggedIn.value = true
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    login,
    logout,
    initAuth
  }
})
