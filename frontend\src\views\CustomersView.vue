<template>
  <div class="customers-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>客户管理</h1>
    </div>

    <!-- 操作栏 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索客户编码、名称或联系人"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增客户
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="customers"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column prop="code" label="编码" :min-width="isMobile ? 100 : 120" />
        <el-table-column prop="name" label="名称" :min-width="isMobile ? 120 : 150" />
        <el-table-column v-if="!isMobile" prop="contact_person" label="联系人" min-width="100" />
        <el-table-column v-if="!isMobile" prop="phone" label="电话" min-width="120" />
        <el-table-column v-if="!isMobile" prop="credit_limit" label="信用额度" min-width="100">
          <template #default="{ row }">
            ¥{{ (row.credit_limit || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" :min-width="isMobile ? 80 : 100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" :min-width="isMobile ? 120 : 150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editCustomer(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteCustomer(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCustomer ? '编辑客户' : '新增客户'"
      width="600px"
    >
      <el-form
        ref="customerFormRef"
        :model="customerForm"
        :rules="customerRules"
        label-width="100px"
      >
        <el-form-item label="编码" prop="code">
          <el-input v-model="customerForm.code" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="customerForm.name" />
        </el-form-item>
        <el-form-item label="联系人" prop="contact_person">
          <el-input v-model="customerForm.contact_person" />
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="customerForm.phone" />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="customerForm.address" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item label="信用额度" prop="credit_limit">
          <el-input-number v-model="customerForm.credit_limit" :min="0" :precision="2" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitCustomer">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { customerApi, type Customer, type CustomerForm } from '@/api/customers'
import { useWindowSize } from '@/composables/useWindowSize'

const { isMobile } = useWindowSize()

// 响应式数据
const customers = ref<Customer[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showAddDialog = ref(false)
const editingCustomer = ref<Customer | null>(null)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单引用和数据
const customerFormRef = ref<FormInstance>()
const customerForm = reactive<CustomerForm>({
  code: '',
  name: '',
  contact_person: '',
  phone: '',
  address: '',
  credit_limit: 0
})

// 表单验证规则
const customerRules: FormRules = {
  code: [{ required: true, message: '请输入客户编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入客户名称', trigger: 'blur' }]
}

// 获取客户列表
async function fetchCustomers() {
  loading.value = true
  try {
    const response = await customerApi.getCustomers({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    })
    
    if (response.success && response.data) {
      customers.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取客户列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取客户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1
  fetchCustomers()
}

// 分页处理
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchCustomers()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchCustomers()
}

// 编辑客户
function editCustomer(customer: Customer) {
  editingCustomer.value = customer
  Object.assign(customerForm, {
    code: customer.code,
    name: customer.name,
    contact_person: customer.contact_person || '',
    phone: customer.phone || '',
    address: customer.address || '',
    credit_limit: customer.credit_limit || 0
  })
  showAddDialog.value = true
}

// 删除客户
async function deleteCustomer(customer: Customer) {
  try {
    await ElMessageBox.confirm(
      `确定要删除客户 "${customer.name}" 吗？`,
      '确认删除',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )
    
    const response = await customerApi.deleteCustomer(customer.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchCustomers()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 取消对话框
function cancelDialog() {
  showAddDialog.value = false
  editingCustomer.value = null
  customerFormRef.value?.resetFields()
  Object.assign(customerForm, {
    code: '', name: '', contact_person: '', phone: '', address: '', credit_limit: 0
  })
}

// 提交客户
async function submitCustomer() {
  if (!customerFormRef.value) return
  
  const valid = await customerFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  submitLoading.value = true
  
  try {
    let response
    if (editingCustomer.value) {
      response = await customerApi.updateCustomer(editingCustomer.value.id, customerForm)
    } else {
      response = await customerApi.createCustomer(customerForm)
    }
    
    if (response.success) {
      ElMessage.success(editingCustomer.value ? '更新成功' : '创建成功')
      showAddDialog.value = false
      fetchCustomers()
      cancelDialog()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchCustomers()
})
</script>

<style scoped>
.customers-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.operations-left {
  flex: 1;
  min-width: 200px;
}

.operations-right {
  flex-shrink: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mobile-table {
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .customers-container {
    padding: 10px;
  }
  
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left {
    margin-bottom: 10px;
  }
  
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 10px;
  }
}
</style>
