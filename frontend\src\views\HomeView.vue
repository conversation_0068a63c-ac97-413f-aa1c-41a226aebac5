<template>
  <div class="dashboard">
    <div class="welcome-section">
      <h1>欢迎使用ERP进销存管理系统</h1>
      <p>当前用户：{{ authStore.user?.username }} ({{ authStore.user?.role }})</p>
    </div>

    <div class="stats-grid">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40" color="#409eff"><Box /></el-icon>
          </div>
          <div class="stat-info">
            <h3>原材料</h3>
            <p class="stat-number">{{ materialCount }}</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40" color="#67c23a"><Goods /></el-icon>
          </div>
          <div class="stat-info">
            <h3>成品</h3>
            <p class="stat-number">{{ productCount }}</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40" color="#e6a23c"><User /></el-icon>
          </div>
          <div class="stat-info">
            <h3>用户</h3>
            <p class="stat-number">1</p>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40" color="#f56c6c"><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <h3>系统状态</h3>
            <p class="stat-text">正常运行</p>
          </div>
        </div>
      </el-card>
    </div>

    <div class="quick-actions">
      <h2>快速操作</h2>
      <div class="action-buttons">
        <el-button type="primary" @click="$router.push('/materials')">
          <el-icon><Plus /></el-icon>
          新增原材料
        </el-button>
        <el-button type="success" @click="$router.push('/products')">
          <el-icon><Plus /></el-icon>
          新增成品
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Box, Goods, User, TrendCharts, Plus } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { materialApi } from '@/api/materials'

const authStore = useAuthStore()
const materialCount = ref(0)
const productCount = ref(0)

// 获取统计数据
async function fetchStats() {
  try {
    const materialResponse = await materialApi.getMaterials({ limit: 1 })
    if (materialResponse.success && materialResponse.data) {
      materialCount.value = materialResponse.data.total
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-section h1 {
  color: #333;
  margin-bottom: 10px;
}

.welcome-section p {
  color: #666;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  width: 100%;
}

/* 响应式网格 */
@media (max-width: 767px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) and (max-width: 1399px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1400px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 16px;
}

.stat-number {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-text {
  margin: 0;
  font-size: 14px;
  color: #67c23a;
}

.quick-actions h2 {
  color: #333;
  margin-bottom: 15px;
}

.action-buttons {
  display: flex;
  gap: 15px;
}

/* 移动端适配 */
@media (max-width: 767px) {
  .dashboard {
    padding: 16px;
  }

  .welcome-section h1 {
    font-size: 20px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .action-buttons .el-button {
    width: 100%;
  }
}
</style>
