<template>
  <div class="inventory-alerts-view">
    <div class="page-header">
      <h1>库存预警</h1>
      <p>查看库存预警信息和处理状态</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="预警状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择预警状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="已解决" value="resolved" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预警列表 -->
    <el-card class="table-card">
      <DataTable
        :data="tableData"
        :columns="columns"
        :loading="loading"
        :total="pagination.total"
        @page-change="handlePageChange"
      >
        <template #item_type="{ row }">
          <el-tag :type="row.item_type === 'material' ? 'info' : 'success'">
            {{ row.item_type === 'material' ? '原材料' : '成品' }}
          </el-tag>
        </template>
        
        <template #alert_type="{ row }">
          <el-tag :type="getAlertTypeColor(row.alert_type)">
            {{ getAlertTypeText(row.alert_type) }}
          </el-tag>
        </template>
        
        <template #current_stock="{ row }">
          <span :class="getStockClass(row.alert_type)">
            {{ row.current_stock }}
          </span>
        </template>
        
        <template #status="{ row }">
          <el-tag :type="row.status === 'active' ? 'danger' : 'success'">
            {{ row.status === 'active' ? '活跃' : '已解决' }}
          </el-tag>
        </template>
        
        <template #created_at="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
        
        <template #resolved_at="{ row }">
          {{ row.resolved_at ? formatDateTime(row.resolved_at) : '-' }}
        </template>
      </DataTable>
    </el-card>

    <!-- 预警统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon zero-stock">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ zeroStockCount }}</div>
              <div class="stats-label">零库存预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon low-stock">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ lowStockCount }}</div>
              <div class="stats-label">低库存预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon high-stock">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ highStockCount }}</div>
              <div class="stats-label">高库存预警</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ totalActiveAlerts }}</div>
              <div class="stats-label">活跃预警总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, CircleClose, Warning, TrendCharts, DataAnalysis } from '@element-plus/icons-vue'
import DataTable from '@/components/DataTable.vue'
import { inventoryApi, type InventoryAlert } from '@/api/inventory'

// 搜索表单
const searchForm = reactive({
  status: 'active'
})

// 表格数据
const tableData = ref<InventoryAlert[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 表格列配置
const columns = [
  { prop: 'item_code', label: '编码', width: 120 },
  { prop: 'item_name', label: '名称', width: 200 },
  { prop: 'item_type', label: '类型', width: 100, slot: 'item_type' },
  { prop: 'alert_type', label: '预警类型', width: 120, slot: 'alert_type' },
  { prop: 'current_stock', label: '当前库存', width: 120, slot: 'current_stock' },
  { prop: 'threshold_value', label: '阈值', width: 100 },
  { prop: 'item_unit', label: '单位', width: 80 },
  { prop: 'status', label: '状态', width: 100, slot: 'status' },
  { prop: 'created_at', label: '预警时间', width: 180, slot: 'created_at' },
  { prop: 'resolved_at', label: '解决时间', width: 180, slot: 'resolved_at' }
]

// 统计数据
const zeroStockCount = computed(() =>
  (tableData.value || []).filter(item => item.alert_type === 'zero_stock' && item.status === 'active').length
)

const lowStockCount = computed(() =>
  (tableData.value || []).filter(item => item.alert_type === 'low_stock' && item.status === 'active').length
)

const highStockCount = computed(() =>
  (tableData.value || []).filter(item => item.alert_type === 'high_stock' && item.status === 'active').length
)

const totalActiveAlerts = computed(() =>
  (tableData.value || []).filter(item => item.status === 'active').length
)

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      status: searchForm.status || undefined
    }
    
    const res = await inventoryApi.getInventoryAlerts(params)
    tableData.value = res.data.data || []
    pagination.total = res.data.total || 0
  } catch (error) {
    console.error('获取库存预警失败:', error)
    ElMessage.error('获取库存预警失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.status = 'active'
  pagination.page = 1
  fetchData()
}

// 分页处理
const handlePageChange = (page: number, size?: number) => {
  pagination.page = page
  if (size !== undefined) {
    pagination.limit = size
  }
  fetchData()
}

// 工具函数
const getAlertTypeColor = (type: string) => {
  switch (type) {
    case 'zero_stock': return 'danger'
    case 'low_stock': return 'warning'
    case 'high_stock': return 'info'
    default: return 'info'
  }
}

const getAlertTypeText = (type: string) => {
  switch (type) {
    case 'zero_stock': return '零库存'
    case 'low_stock': return '低库存'
    case 'high_stock': return '高库存'
    default: return type
  }
}

const getStockClass = (alertType: string) => {
  switch (alertType) {
    case 'zero_stock': return 'text-danger'
    case 'low_stock': return 'text-warning'
    case 'high_stock': return 'text-info'
    default: return 'text-success'
  }
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.inventory-alerts-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  cursor: default;
}

.stats-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.zero-stock {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #f56c6c;
}

.stats-icon.low-stock {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #e6a23c;
}

.stats-icon.high-stock {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #409eff;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}
</style>
