<template>
  <div class="inventory-view">
    <div class="page-header">
      <h1>库存管理</h1>
      <p>实时查看原材料和成品库存状态</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon material">
                <el-icon><Box /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ materialCount }}</div>
                <div class="stats-label">原材料种类</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon product">
                <el-icon><Goods /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ productCount }}</div>
                <div class="stats-label">成品种类</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ alertCount }}</div>
                <div class="stats-label">库存预警</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon zero">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ zeroStockCount }}</div>
                <div class="stats-label">零库存</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 功能导航 -->
    <div class="function-nav">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="nav-card" @click="goToMaterialsInventory">
            <div class="nav-content">
              <el-icon class="nav-icon"><Box /></el-icon>
              <h3>原材料库存</h3>
              <p>查看原材料库存状态和变动历史</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="nav-card" @click="goToProductsInventory">
            <div class="nav-content">
              <el-icon class="nav-icon"><Goods /></el-icon>
              <h3>成品库存</h3>
              <p>查看成品库存状态和变动历史</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="nav-card" @click="goToInventoryAlerts">
            <div class="nav-content">
              <el-icon class="nav-icon"><Warning /></el-icon>
              <h3>库存预警</h3>
              <p>查看库存预警信息和处理状态</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近预警 -->
    <el-card class="recent-alerts">
      <template #header>
        <div class="card-header">
          <span>最近预警</span>
          <el-button type="primary" size="small" @click="goToInventoryAlerts">
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-table :data="recentAlerts" style="width: 100%">
        <el-table-column prop="item_code" label="编码" width="120" />
        <el-table-column prop="item_name" label="名称" width="200" />
        <el-table-column prop="item_type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.item_type === 'material' ? 'info' : 'success'">
              {{ row.item_type === 'material' ? '原材料' : '成品' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="alert_type" label="预警类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getAlertTypeColor(row.alert_type)">
              {{ getAlertTypeText(row.alert_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="current_stock" label="当前库存" width="120" />
        <el-table-column prop="item_unit" label="单位" width="80" />
        <el-table-column prop="created_at" label="预警时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'danger' : 'success'">
              {{ row.status === 'active' ? '活跃' : '已解决' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Box, Goods, Warning, CircleClose } from '@element-plus/icons-vue'
import { inventoryApi, type InventoryAlert } from '@/api/inventory'

const router = useRouter()

// 统计数据
const materialCount = ref(0)
const productCount = ref(0)
const alertCount = ref(0)
const zeroStockCount = ref(0)

// 最近预警
const recentAlerts = ref<InventoryAlert[]>([])

// 获取统计数据
const getStats = async () => {
  try {
    // 获取原材料统计
    const materialRes = await inventoryApi.getMaterialsInventory({ limit: 1 })
    materialCount.value = materialRes.data.data.total

    // 获取成品统计
    const productRes = await inventoryApi.getProductsInventory({ limit: 1 })
    productCount.value = productRes.data.data.total

    // 获取预警统计
    const alertRes = await inventoryApi.getInventoryAlerts({ status: 'active', limit: 1 })
    alertCount.value = alertRes.data.data.total

    // 获取零库存统计
    const zeroStockRes = await inventoryApi.getInventoryList({ alert_status: 'zero_stock', limit: 1 })
    zeroStockCount.value = zeroStockRes.data.data.total

  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 获取最近预警
const getRecentAlerts = async () => {
  try {
    const res = await inventoryApi.getInventoryAlerts({ status: 'active', limit: 5 })
    recentAlerts.value = res.data.data.data
  } catch (error) {
    console.error('获取最近预警失败:', error)
  }
}

// 导航函数
const goToMaterialsInventory = () => {
  router.push('/inventory/materials')
}

const goToProductsInventory = () => {
  router.push('/inventory/products')
}

const goToInventoryAlerts = () => {
  router.push('/inventory/alerts')
}

// 工具函数
const getAlertTypeColor = (type: string) => {
  switch (type) {
    case 'zero_stock': return 'danger'
    case 'low_stock': return 'warning'
    case 'high_stock': return 'info'
    default: return 'info'
  }
}

const getAlertTypeText = (type: string) => {
  switch (type) {
    case 'zero_stock': return '零库存'
    case 'low_stock': return '低库存'
    case 'high_stock': return '高库存'
    default: return type
  }
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  getStats()
  getRecentAlerts()
})
</script>

<style scoped>
.inventory-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stats-card {
  cursor: default;
}

.stats-content {
  display: flex;
  align-items: center;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.material {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.product {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.warning {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #e6a23c;
}

.stats-icon.zero {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #f56c6c;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.function-nav {
  margin-bottom: 24px;
}

.nav-card {
  cursor: pointer;
  transition: all 0.3s;
}

.nav-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-content {
  text-align: center;
  padding: 20px;
}

.nav-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 16px;
}

.nav-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.nav-content p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.recent-alerts {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
