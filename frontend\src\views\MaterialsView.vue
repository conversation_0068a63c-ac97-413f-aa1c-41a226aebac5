<template>
  <div class="materials-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>原材料管理</h1>
    </div>

    <!-- 操作栏 - 左右分布 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索原材料编码、名称或规格"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增原材料
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="materials"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column
          prop="code"
          label="编码"
          :min-width="isMobile ? 100 : 120"
          :width="isMobile ? 100 : undefined"
        />
        <el-table-column
          prop="name"
          label="名称"
          :min-width="isMobile ? 120 : 150"
          :width="isMobile ? 120 : undefined"
        />
        <el-table-column
          v-if="!isMobile"
          prop="specification"
          label="规格"
          min-width="120"
        />
        <el-table-column
          prop="unit"
          label="单位"
          :min-width="isMobile ? 60 : 80"
          :width="isMobile ? 60 : undefined"
        />
        <el-table-column
          prop="cost_price"
          label="成本价"
          :min-width="isMobile ? 90 : 100"
          :width="isMobile ? 90 : undefined"
        >
          <template #default="{ row }">
            ¥{{ row.cost_price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="!isMobile"
          prop="current_stock"
          label="当前库存"
          min-width="100"
        />
        <el-table-column
          v-if="!isMobile"
          prop="stock_min"
          label="最小库存"
          min-width="100"
        />
        <el-table-column
          v-if="!isMobile"
          prop="stock_max"
          label="最大库存"
          min-width="100"
        />
        <el-table-column
          v-if="!isMobile"
          prop="created_at"
          label="创建时间"
          min-width="160"
        >
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          :width="isMobile ? 120 : 150"
          fixed="right"
        >
          <template #default="{ row }">
            <div class="table-actions">
              <el-button
                :size="isMobile ? 'small' : 'default'"
                type="primary"
                link
                @click="editMaterial(row)"
              >
                编辑
              </el-button>
              <el-button
                :size="isMobile ? 'small' : 'default'"
                type="danger"
                link
                @click="deleteMaterial(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingMaterial ? '编辑原材料' : '新增原材料'"
      width="600px"
    >
      <el-form
        ref="materialFormRef"
        :model="materialForm"
        :rules="materialRules"
        label-width="100px"
      >
        <el-form-item label="编码" prop="code">
          <el-input v-model="materialForm.code" />
        </el-form-item>
        
        <el-form-item label="名称" prop="name">
          <el-input v-model="materialForm.name" />
        </el-form-item>
        
        <el-form-item label="规格" prop="specification">
          <el-input v-model="materialForm.specification" />
        </el-form-item>
        
        <el-form-item label="单位" prop="unit">
          <el-input v-model="materialForm.unit" />
        </el-form-item>
        
        <el-form-item label="成本价" prop="cost_price">
          <el-input-number
            v-model="materialForm.cost_price"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="最小库存" prop="stock_min">
          <el-input-number
            v-model="materialForm.stock_min"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="最大库存" prop="stock_max">
          <el-input-number
            v-model="materialForm.stock_max"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="当前库存" prop="current_stock">
          <el-input-number
            v-model="materialForm.current_stock"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitMaterial">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { materialApi, type Material, type MaterialForm } from '@/api/materials'
import { useWindowSize } from '@/composables/useWindowSize'

// 使用全局窗口大小管理器
const { isMobile, isTablet, isDesktop, isLargeScreen } = useWindowSize()

// 响应式数据
const materials = ref<Material[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showAddDialog = ref(false)
const editingMaterial = ref<Material | null>(null)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单引用和数据
const materialFormRef = ref<FormInstance>()
const materialForm = reactive<MaterialForm>({
  code: '',
  name: '',
  specification: '',
  unit: '',
  cost_price: 0,
  stock_min: 0,
  stock_max: 0,
  current_stock: 0
})

// 表单验证规则
const materialRules: FormRules = {
  code: [
    { required: true, message: '请输入原材料编码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入原材料名称', trigger: 'blur' }
  ],
  unit: [
    { required: true, message: '请输入单位', trigger: 'blur' }
  ]
}

// 获取原材料列表
async function fetchMaterials() {
  loading.value = true
  try {
    const response = await materialApi.getMaterials({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    })
    
    if (response.success && response.data) {
      materials.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取原材料列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取原材料列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1
  fetchMaterials()
}

// 分页处理
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchMaterials()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchMaterials()
}

// 编辑原材料
function editMaterial(material: Material) {
  editingMaterial.value = material
  Object.assign(materialForm, {
    code: material.code,
    name: material.name,
    specification: material.specification || '',
    unit: material.unit,
    cost_price: material.cost_price,
    stock_min: material.stock_min,
    stock_max: material.stock_max,
    current_stock: material.current_stock
  })
  showAddDialog.value = true
}

// 删除原材料
async function deleteMaterial(material: Material) {
  try {
    await ElMessageBox.confirm(
      `确定要删除原材料 "${material.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await materialApi.deleteMaterial(material.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchMaterials()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 提交原材料
async function submitMaterial() {
  if (!materialFormRef.value) return
  
  const valid = await materialFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  submitLoading.value = true
  
  try {
    let response
    if (editingMaterial.value) {
      response = await materialApi.updateMaterial(editingMaterial.value.id, materialForm)
    } else {
      response = await materialApi.createMaterial(materialForm)
    }
    
    if (response.success) {
      ElMessage.success(editingMaterial.value ? '更新成功' : '创建成功')
      showAddDialog.value = false
      fetchMaterials()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 取消编辑
function cancelEdit() {
  showAddDialog.value = false
  editingMaterial.value = null
  Object.assign(materialForm, {
    code: '',
    name: '',
    specification: '',
    unit: '',
    cost_price: 0,
    stock_min: 0,
    stock_max: 0,
    current_stock: 0
  })
  materialFormRef.value?.resetFields()
}

// 格式化日期
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchMaterials()
})
</script>

<style scoped>
.materials-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
}

.operations-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.operations-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-container {
  overflow-x: auto;
  width: 100%;
}

.mobile-table {
  min-width: 600px;
}

/* 确保表格充分利用空间 */
.el-table {
  width: 100% !important;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 响应式样式 */
@media (max-width: 767px) {
  .operations-bar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .operations-left {
    width: 100%;
  }

  .operations-right {
    width: 100%;
    justify-content: center;
  }

  .page-title h1 {
    font-size: 20px;
  }

  .pagination {
    text-align: center;
  }

  .materials-container {
    padding: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .materials-container {
    padding: 18px;
  }

  .page-title h1 {
    font-size: 22px;
  }

  .operations-bar {
    gap: 20px;
  }
}

@media (min-width: 1024px) {
  .materials-container {
    padding: 20px;
  }

  .operations-bar {
    margin-bottom: 24px;
    gap: 24px;
  }

  .page-title {
    margin-bottom: 24px;
  }
}

@media (min-width: 1400px) {
  .materials-container {
    padding: 24px;
  }

  .search-bar {
    margin-bottom: 24px;
  }
}
</style>
