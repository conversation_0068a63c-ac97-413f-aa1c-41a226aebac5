<template>
  <div class="production-completions-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>生产完工管理</h1>
    </div>

    <!-- 操作栏 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索完工单号或成品"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增完工单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="productionCompletions"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column prop="completion_no" label="完工单号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="production_plan_no" label="生产计划号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="product_name" label="成品" :min-width="isMobile ? 100 : 120" />
        <el-table-column v-if="!isMobile" prop="completion_date" label="完工日期" min-width="100" />
        <el-table-column prop="completed_quantity" label="完工数量" :min-width="isMobile ? 90 : 100" />
        <el-table-column prop="status" label="状态" :min-width="isMobile ? 80 : 100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'confirmed' ? 'success' : 'warning'">
              {{ row.status === 'confirmed' ? '已确认' : '草稿' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" :min-width="isMobile ? 100 : 120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewCompletion(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建完工单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增生产完工单"
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="completionFormRef"
        :model="completionForm"
        :rules="completionRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产计划" prop="production_plan_id">
              <el-select 
                v-model="completionForm.production_plan_id" 
                placeholder="请选择生产计划" 
                style="width: 100%"
                @change="onProductionPlanChange"
              >
                <el-option
                  v-for="plan in inProgressPlans"
                  :key="plan.id"
                  :label="`${plan.plan_no} - ${plan.product_name}`"
                  :value="plan.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="完工数量" prop="completed_quantity">
              <el-input-number 
                v-model="completionForm.completed_quantity" 
                :min="1" 
                :precision="2" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="完工日期" prop="completion_date">
              <el-date-picker
                v-model="completionForm.completion_date"
                type="date"
                placeholder="选择完工日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="completionForm.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 物料消耗明细 -->
      <div v-if="selectedPlanItems.length > 0" class="materials-section">
        <h3>物料消耗明细</h3>
        <el-table :data="completionForm.materials" style="width: 100%">
          <el-table-column prop="material_name" label="原材料" min-width="150" />
          <el-table-column prop="required_quantity" label="需要数量" width="100" />
          <el-table-column label="消耗数量" width="120">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.consumed_quantity" 
                :min="0" 
                :precision="2" 
                style="width: 100%"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitCompletion">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 完工单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="生产完工单详情" width="70%">
      <div v-if="currentCompletion">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="完工单号">{{ currentCompletion.completion_no }}</el-descriptions-item>
          <el-descriptions-item label="生产计划号">{{ currentCompletion.production_plan_no }}</el-descriptions-item>
          <el-descriptions-item label="成品">{{ currentCompletion.product_name }}</el-descriptions-item>
          <el-descriptions-item label="完工数量">{{ currentCompletion.completed_quantity }}</el-descriptions-item>
          <el-descriptions-item label="完工日期">{{ currentCompletion.completion_date }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentCompletion.status === 'confirmed' ? 'success' : 'warning'">
              {{ currentCompletion.status === 'confirmed' ? '已确认' : '草稿' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentCompletion.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px;">物料消耗明细</h3>
        <el-table :data="currentCompletion.items" style="width: 100%">
          <el-table-column prop="material_code" label="原材料编码" />
          <el-table-column prop="material_name" label="原材料名称" />
          <el-table-column prop="consumed_quantity" label="消耗数量" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { productionCompletionApi, type ProductionCompletion, type ProductionCompletionForm } from '@/api/productionCompletions'
import { productionPlanApi, type ProductionPlan } from '@/api/productionPlans'
import { useWindowSize } from '@/composables/useWindowSize'

const { isMobile } = useWindowSize()

// 响应式数据
const productionCompletions = ref<ProductionCompletion[]>([])
const inProgressPlans = ref<ProductionPlan[]>([])
const selectedPlanItems = ref<any[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentCompletion = ref<ProductionCompletion | null>(null)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单数据
const completionFormRef = ref<FormInstance>()
const completionForm = reactive<ProductionCompletionForm>({
  production_plan_id: 0,
  completed_quantity: 1,
  completion_date: '',
  remark: '',
  materials: []
})

// 表单验证规则
const completionRules: FormRules = {
  production_plan_id: [{ required: true, message: '请选择生产计划', trigger: 'change' }],
  completed_quantity: [{ required: true, message: '请输入完工数量', trigger: 'blur' }],
  completion_date: [{ required: true, message: '请选择完工日期', trigger: 'change' }]
}

// 获取数据的方法
async function fetchProductionCompletions() {
  loading.value = true
  try {
    const response = await productionCompletionApi.getProductionCompletions({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    })
    
    if (response.success && response.data) {
      productionCompletions.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取生产完工单列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取生产完工单列表失败')
  } finally {
    loading.value = false
  }
}

async function fetchInProgressPlans() {
  try {
    const response = await productionPlanApi.getProductionPlans({ 
      limit: 1000, 
      status: 'in_progress' 
    })
    if (response.success && response.data) {
      inProgressPlans.value = response.data.data
    }
  } catch (error) {
    console.error('获取进行中生产计划失败:', error)
  }
}

// 搜索和分页
function handleSearch() {
  currentPage.value = 1
  fetchProductionCompletions()
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchProductionCompletions()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchProductionCompletions()
}

// 完工单操作
async function viewCompletion(completion: ProductionCompletion) {
  try {
    const response = await productionCompletionApi.getProductionCompletion(completion.id)
    if (response.success && response.data) {
      currentCompletion.value = response.data
      showDetailDialog.value = true
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取完工单详情失败')
  }
}

// 生产计划变化处理
async function onProductionPlanChange() {
  if (!completionForm.production_plan_id) {
    selectedPlanItems.value = []
    completionForm.materials = []
    return
  }
  
  try {
    const response = await productionPlanApi.getProductionPlan(completionForm.production_plan_id)
    if (response.success && response.data) {
      selectedPlanItems.value = response.data.items || []
      completionForm.materials = selectedPlanItems.value.map(item => ({
        material_id: item.material_id,
        material_name: item.material_name,
        required_quantity: item.required_quantity,
        consumed_quantity: item.required_quantity
      }))
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取生产计划详情失败')
  }
}

// 对话框操作
function cancelDialog() {
  showCreateDialog.value = false
  completionFormRef.value?.resetFields()
  Object.assign(completionForm, {
    production_plan_id: 0,
    completed_quantity: 1,
    completion_date: '',
    remark: '',
    materials: []
  })
  selectedPlanItems.value = []
}

async function submitCompletion() {
  if (!completionFormRef.value) return
  
  const valid = await completionFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  if (completionForm.materials.length === 0) {
    ElMessage.error('请选择生产计划')
    return
  }
  
  submitLoading.value = true
  
  try {
    const response = await productionCompletionApi.createProductionCompletion(completionForm)
    
    if (response.success) {
      ElMessage.success('生产完工单创建成功，库存已更新')
      showCreateDialog.value = false
      fetchProductionCompletions()
      cancelDialog()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchProductionCompletions()
  fetchInProgressPlans()
})
</script>

<style scoped>
.production-completions-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.operations-left {
  flex: 1;
  min-width: 200px;
}

.operations-right {
  flex-shrink: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.materials-section {
  margin-top: 20px;
}

.materials-section h3 {
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.mobile-table {
  font-size: 14px;
}

@media (max-width: 768px) {
  .production-completions-container {
    padding: 10px;
  }
  
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left {
    margin-bottom: 10px;
  }
  
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 10px;
  }
}
</style>
