<template>
  <div class="purchase-orders-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>采购订单管理</h1>
    </div>

    <!-- 操作栏 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索订单号或供应商"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-left: 10px;" @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="草稿" value="draft" />
          <el-option label="待审核" value="pending" />
          <el-option label="已审核" value="approved" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增采购订单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="purchaseOrders"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column prop="order_no" label="订单号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="supplier_name" label="供应商" :min-width="isMobile ? 100 : 120" />
        <el-table-column v-if="!isMobile" prop="order_date" label="订单日期" min-width="100" />
        <el-table-column v-if="!isMobile" prop="expected_date" label="预期到货" min-width="100" />
        <el-table-column prop="total_amount" label="总金额" :min-width="isMobile ? 90 : 100">
          <template #default="{ row }">
            ¥{{ row.total_amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" :min-width="isMobile ? 80 : 100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" :min-width="isMobile ? 150 : 200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewOrder(row)">查看</el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="approveOrder(row)"
            >
              审核
            </el-button>
            <el-button
              v-if="row.status === 'draft'"
              type="primary"
              size="small"
              @click="submitForApproval(row)"
            >
              提交审核
            </el-button>
            <el-button
              v-if="row.status === 'draft'"
              type="warning"
              size="small"
              @click="editOrder(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="row.status !== 'completed'" 
              type="danger" 
              size="small" 
              @click="deleteOrder(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingOrder ? '编辑采购订单' : '新增采购订单'"
      width="80%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="orderFormRef"
        :model="orderForm"
        :rules="orderRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier_id">
              <el-select v-model="orderForm.supplier_id" placeholder="请选择供应商" style="width: 100%">
                <el-option
                  v-for="supplier in suppliers"
                  :key="supplier.id"
                  :label="supplier.name"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="订单日期" prop="order_date">
              <el-date-picker
                v-model="orderForm.order_date"
                type="date"
                placeholder="选择订单日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预期到货" prop="expected_date">
              <el-date-picker
                v-model="orderForm.expected_date"
                type="date"
                placeholder="选择预期到货日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="orderForm.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 订单明细 -->
      <div class="order-items-section">
        <div class="section-header">
          <h3>订单明细</h3>
          <el-button type="primary" size="small" @click="addOrderItem">
            <el-icon><Plus /></el-icon>
            添加明细
          </el-button>
        </div>
        
        <el-table :data="orderForm.items" style="width: 100%">
          <el-table-column label="原材料" min-width="150">
            <template #default="{ row, $index }">
              <el-select 
                v-model="row.material_id" 
                placeholder="选择原材料" 
                style="width: 100%"
                @change="onMaterialChange(row, $index)"
              >
                <el-option
                  v-for="material in materials"
                  :key="material.id"
                  :label="`${material.code} - ${material.name}`"
                  :value="material.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="数量" width="120">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.quantity" 
                :min="0" 
                :precision="2" 
                style="width: 100%"
                @change="calculateItemTotal(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="单价" width="120">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.unit_price" 
                :min="0" 
                :precision="2" 
                style="width: 100%"
                @change="calculateItemTotal(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="小计" width="120">
            <template #default="{ row }">
              ¥{{ (row.quantity * row.unit_price || 0).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeOrderItem($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="total-amount">
          <strong>总金额: ¥{{ totalAmount.toFixed(2) }}</strong>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitOrder">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="采购订单详情" width="80%">
      <div v-if="currentOrder">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ currentOrder.order_no }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ currentOrder.supplier_name }}</el-descriptions-item>
          <el-descriptions-item label="订单日期">{{ currentOrder.order_date }}</el-descriptions-item>
          <el-descriptions-item label="预期到货">{{ currentOrder.expected_date || '-' }}</el-descriptions-item>
          <el-descriptions-item label="总金额">¥{{ currentOrder.total_amount.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentOrder.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px;">订单明细</h3>
        <el-table :data="currentOrder.items" style="width: 100%">
          <el-table-column prop="material_code" label="原材料编码" />
          <el-table-column prop="material_name" label="原材料名称" />
          <el-table-column prop="quantity" label="数量" />
          <el-table-column prop="unit_price" label="单价">
            <template #default="{ row }">
              ¥{{ row.unit_price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="total_price" label="小计">
            <template #default="{ row }">
              ¥{{ row.total_price.toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { purchaseOrderApi, type PurchaseOrder, type PurchaseOrderForm } from '@/api/purchaseOrders'
import { supplierApi, type Supplier } from '@/api/suppliers'
import { materialApi, type Material } from '@/api/materials'
import { useWindowSize } from '@/composables/useWindowSize'

const { isMobile } = useWindowSize()

// 响应式数据
const purchaseOrders = ref<PurchaseOrder[]>([])
const suppliers = ref<Supplier[]>([])
const materials = ref<Material[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingOrder = ref<PurchaseOrder | null>(null)
const currentOrder = ref<PurchaseOrder | null>(null)
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单数据
const orderFormRef = ref<FormInstance>()
const orderForm = reactive<PurchaseOrderForm>({
  supplier_id: 0,
  order_date: '',
  expected_date: '',
  remark: '',
  items: []
})

// 表单验证规则
const orderRules: FormRules = {
  supplier_id: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  order_date: [{ required: true, message: '请选择订单日期', trigger: 'change' }]
}

// 计算总金额
const totalAmount = computed(() => {
  return orderForm.items.reduce((sum, item) => sum + (item.quantity * item.unit_price || 0), 0)
})

// 状态相关方法
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    approved: 'success',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    pending: '待审核',
    approved: '已审核',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取数据的方法
async function fetchPurchaseOrders() {
  loading.value = true
  try {
    const response = await purchaseOrderApi.getPurchaseOrders({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value
    })
    
    if (response.success && response.data) {
      purchaseOrders.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取采购订单列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取采购订单列表失败')
  } finally {
    loading.value = false
  }
}

async function fetchSuppliers() {
  try {
    const response = await supplierApi.getSuppliers({ limit: 1000 })
    if (response.success && response.data) {
      suppliers.value = response.data.data
    }
  } catch (error) {
    console.error('获取供应商列表失败:', error)
  }
}

async function fetchMaterials() {
  try {
    const response = await materialApi.getMaterials({ limit: 1000 })
    if (response.success && response.data) {
      materials.value = response.data.data
    }
  } catch (error) {
    console.error('获取原材料列表失败:', error)
  }
}

// 搜索和分页
function handleSearch() {
  currentPage.value = 1
  fetchPurchaseOrders()
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchPurchaseOrders()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchPurchaseOrders()
}

// 订单操作
async function viewOrder(order: PurchaseOrder) {
  try {
    const response = await purchaseOrderApi.getPurchaseOrder(order.id)
    if (response.success && response.data) {
      currentOrder.value = response.data
      showDetailDialog.value = true
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取订单详情失败')
  }
}

async function approveOrder(order: PurchaseOrder) {
  try {
    await ElMessageBox.confirm(
      `确定要审核采购订单 "${order.order_no}" 吗？`,
      '确认审核',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )

    const response = await purchaseOrderApi.approvePurchaseOrder(order.id)
    if (response.success) {
      ElMessage.success('审核成功')
      fetchPurchaseOrders()
    } else {
      ElMessage.error(response.message || '审核失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '审核失败')
    }
  }
}

async function submitForApproval(order: PurchaseOrder) {
  try {
    await ElMessageBox.confirm(
      `确定要提交采购订单 "${order.order_no}" 进行审核吗？`,
      '确认提交',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )

    const response = await purchaseOrderApi.updatePurchaseOrderStatus(order.id, 'pending')
    if (response.success) {
      ElMessage.success('提交成功，等待审核')
      fetchPurchaseOrders()
    } else {
      ElMessage.error(response.message || '提交失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '提交失败')
    }
  }
}

function editOrder(order: PurchaseOrder) {
  // 编辑功能暂时简化，只允许修改状态
  ElMessage.info('编辑功能开发中')
}

async function deleteOrder(order: PurchaseOrder) {
  try {
    await ElMessageBox.confirm(
      `确定要删除采购订单 "${order.order_no}" 吗？`,
      '确认删除',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )
    
    const response = await purchaseOrderApi.deletePurchaseOrder(order.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchPurchaseOrders()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 订单明细操作
function addOrderItem() {
  orderForm.items.push({
    material_id: 0,
    quantity: 1,
    unit_price: 0
  })
}

function removeOrderItem(index: number) {
  orderForm.items.splice(index, 1)
}

function onMaterialChange(item: any, index: number) {
  const material = materials.value.find(m => m.id === item.material_id)
  if (material) {
    item.unit_price = material.cost_price || 0
    calculateItemTotal(item, index)
  }
}

function calculateItemTotal(item: any, index: number) {
  // 计算会自动通过computed属性更新总金额
}

// 对话框操作
function cancelDialog() {
  showCreateDialog.value = false
  editingOrder.value = null
  orderFormRef.value?.resetFields()
  Object.assign(orderForm, {
    supplier_id: 0,
    order_date: '',
    expected_date: '',
    remark: '',
    items: []
  })
}

async function submitOrder() {
  if (!orderFormRef.value) return
  
  const valid = await orderFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  if (orderForm.items.length === 0) {
    ElMessage.error('请添加订单明细')
    return
  }
  
  // 验证明细数据
  for (const item of orderForm.items) {
    if (!item.material_id || item.quantity <= 0 || item.unit_price < 0) {
      ElMessage.error('请完善订单明细信息')
      return
    }
  }
  
  submitLoading.value = true
  
  try {
    const response = await purchaseOrderApi.createPurchaseOrder(orderForm)
    
    if (response.success) {
      ElMessage.success('采购订单创建成功')
      showCreateDialog.value = false
      fetchPurchaseOrders()
      cancelDialog()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchPurchaseOrders()
  fetchSuppliers()
  fetchMaterials()
})
</script>

<style scoped>
.purchase-orders-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.operations-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
}

.operations-right {
  flex-shrink: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.order-items-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h3 {
  margin: 0;
}

.total-amount {
  text-align: right;
  margin-top: 10px;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.mobile-table {
  font-size: 14px;
}

@media (max-width: 768px) {
  .purchase-orders-container {
    padding: 10px;
  }
  
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left {
    margin-bottom: 10px;
    flex-direction: column;
    gap: 10px;
  }
  
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 10px;
  }
}
</style>
