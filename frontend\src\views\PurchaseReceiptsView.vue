<template>
  <div class="purchase-receipts-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>采购入库管理</h1>
    </div>

    <!-- 操作栏 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索入库单号或供应商"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增入库单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="purchaseReceipts"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column prop="receipt_no" label="入库单号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="purchase_order_no" label="采购订单号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="supplier_name" label="供应商" :min-width="isMobile ? 100 : 120" />
        <el-table-column v-if="!isMobile" prop="receipt_date" label="入库日期" min-width="100" />
        <el-table-column prop="total_amount" label="总金额" :min-width="isMobile ? 90 : 100">
          <template #default="{ row }">
            ¥{{ row.total_amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" :min-width="isMobile ? 80 : 100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'confirmed' ? 'success' : 'warning'">
              {{ row.status === 'confirmed' ? '已确认' : '草稿' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" :min-width="isMobile ? 150 : 180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewReceipt(row)">查看</el-button>
            <el-button
              v-if="row.status === 'draft'"
              type="success"
              size="small"
              @click="confirmReceipt(row)"
            >
              确认
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建入库单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增采购入库单"
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="receiptFormRef"
        :model="receiptForm"
        :rules="receiptRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购订单" prop="purchase_order_id">
              <el-select 
                v-model="receiptForm.purchase_order_id" 
                placeholder="请选择采购订单" 
                style="width: 100%"
                @change="onPurchaseOrderChange"
              >
                <el-option
                  v-for="order in approvedOrders"
                  :key="order.id"
                  :label="`${order.order_no} - ${order.supplier_name}`"
                  :value="order.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库日期" prop="receipt_date">
              <el-date-picker
                v-model="receiptForm.receipt_date"
                type="date"
                placeholder="选择入库日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="receiptForm.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <!-- 入库明细 -->
      <div v-if="selectedOrderItems.length > 0" class="receipt-items-section">
        <h3>入库明细</h3>
        <el-table :data="receiptForm.items" style="width: 100%">
          <el-table-column prop="material_name" label="原材料" min-width="150" />
          <el-table-column prop="order_quantity" label="订单数量" width="100" />
          <el-table-column label="入库数量" width="120">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.quantity" 
                :min="0" 
                :max="row.order_quantity"
                :precision="2" 
                style="width: 100%"
                @change="calculateItemTotal(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="单价" width="120">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.unit_price" 
                :min="0" 
                :precision="2" 
                style="width: 100%"
                @change="calculateItemTotal(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="小计" width="120">
            <template #default="{ row }">
              ¥{{ (row.quantity * row.unit_price || 0).toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
        
        <div class="total-amount">
          <strong>总金额: ¥{{ totalAmount.toFixed(2) }}</strong>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitReceipt">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 入库单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="采购入库单详情" width="70%">
      <div v-if="currentReceipt">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="入库单号">{{ currentReceipt.receipt_no }}</el-descriptions-item>
          <el-descriptions-item label="采购订单号">{{ currentReceipt.purchase_order_no }}</el-descriptions-item>
          <el-descriptions-item label="供应商">{{ currentReceipt.supplier_name }}</el-descriptions-item>
          <el-descriptions-item label="入库日期">{{ currentReceipt.receipt_date }}</el-descriptions-item>
          <el-descriptions-item label="总金额">¥{{ currentReceipt.total_amount.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentReceipt.status === 'confirmed' ? 'success' : 'warning'">
              {{ currentReceipt.status === 'confirmed' ? '已确认' : '草稿' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentReceipt.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px;">入库明细</h3>
        <el-table :data="currentReceipt.items" style="width: 100%">
          <el-table-column prop="material_code" label="原材料编码" />
          <el-table-column prop="material_name" label="原材料名称" />
          <el-table-column prop="quantity" label="入库数量" />
          <el-table-column prop="unit_price" label="单价">
            <template #default="{ row }">
              ¥{{ row.unit_price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="total_price" label="小计">
            <template #default="{ row }">
              ¥{{ row.total_price.toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { purchaseReceiptApi, type PurchaseReceipt, type PurchaseReceiptForm } from '@/api/purchaseReceipts'
import { purchaseOrderApi, type PurchaseOrder } from '@/api/purchaseOrders'
import { useWindowSize } from '@/composables/useWindowSize'

const { isMobile } = useWindowSize()

// 响应式数据
const purchaseReceipts = ref<PurchaseReceipt[]>([])
const approvedOrders = ref<PurchaseOrder[]>([])
const selectedOrderItems = ref<any[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentReceipt = ref<PurchaseReceipt | null>(null)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单数据
const receiptFormRef = ref<FormInstance>()
const receiptForm = reactive<PurchaseReceiptForm>({
  purchase_order_id: 0,
  receipt_date: '',
  remark: '',
  items: []
})

// 表单验证规则
const receiptRules: FormRules = {
  purchase_order_id: [{ required: true, message: '请选择采购订单', trigger: 'change' }],
  receipt_date: [{ required: true, message: '请选择入库日期', trigger: 'change' }]
}

// 计算总金额
const totalAmount = computed(() => {
  return receiptForm.items.reduce((sum, item) => sum + (item.quantity * item.unit_price || 0), 0)
})

// 获取数据的方法
async function fetchPurchaseReceipts() {
  loading.value = true
  try {
    const response = await purchaseReceiptApi.getPurchaseReceipts({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    })
    
    if (response.success && response.data) {
      purchaseReceipts.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取采购入库单列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取采购入库单列表失败')
  } finally {
    loading.value = false
  }
}

async function fetchApprovedOrders() {
  try {
    const response = await purchaseOrderApi.getPurchaseOrders({ 
      limit: 1000, 
      status: 'approved' 
    })
    if (response.success && response.data) {
      approvedOrders.value = response.data.data
    }
  } catch (error) {
    console.error('获取已审核采购订单失败:', error)
  }
}

// 搜索和分页
function handleSearch() {
  currentPage.value = 1
  fetchPurchaseReceipts()
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchPurchaseReceipts()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchPurchaseReceipts()
}

// 入库单操作
async function viewReceipt(receipt: PurchaseReceipt) {
  try {
    const response = await purchaseReceiptApi.getPurchaseReceipt(receipt.id)
    if (response.success && response.data) {
      currentReceipt.value = response.data
      showDetailDialog.value = true
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取入库单详情失败')
  }
}

// 采购订单变化处理
async function onPurchaseOrderChange() {
  if (!receiptForm.purchase_order_id) {
    selectedOrderItems.value = []
    receiptForm.items = []
    return
  }
  
  try {
    const response = await purchaseOrderApi.getPurchaseOrder(receiptForm.purchase_order_id)
    if (response.success && response.data) {
      selectedOrderItems.value = response.data.items || []
      receiptForm.items = selectedOrderItems.value.map(item => ({
        material_id: item.material_id,
        material_name: item.material_name,
        order_quantity: item.quantity,
        quantity: item.quantity,
        unit_price: item.unit_price
      }))
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取采购订单详情失败')
  }
}

function calculateItemTotal(item: any, index: number) {
  // 计算会自动通过computed属性更新总金额
}

// 对话框操作
function cancelDialog() {
  showCreateDialog.value = false
  receiptFormRef.value?.resetFields()
  Object.assign(receiptForm, {
    purchase_order_id: 0,
    receipt_date: '',
    remark: '',
    items: []
  })
  selectedOrderItems.value = []
}

async function submitReceipt() {
  if (!receiptFormRef.value) return
  
  const valid = await receiptFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  if (receiptForm.items.length === 0) {
    ElMessage.error('请选择采购订单')
    return
  }
  
  submitLoading.value = true
  
  try {
    const response = await purchaseReceiptApi.createPurchaseReceipt(receiptForm)
    
    if (response.success) {
      ElMessage.success('采购入库单创建成功，请确认后更新库存')
      showCreateDialog.value = false
      fetchPurchaseReceipts()
      cancelDialog()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 确认入库单
async function confirmReceipt(receipt: any) {
  try {
    await ElMessageBox.confirm(
      `确定要确认入库单 "${receipt.receipt_no}" 吗？确认后将更新库存。`,
      '确认入库',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )

    const response = await purchaseReceiptApi.confirmPurchaseReceipt(receipt.id)
    if (response.success) {
      ElMessage.success('入库单确认成功，库存已更新')
      fetchPurchaseReceipts()
    } else {
      ElMessage.error(response.message || '确认失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '确认失败')
    }
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchPurchaseReceipts()
  fetchApprovedOrders()
})
</script>

<style scoped>
.purchase-receipts-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.operations-left {
  flex: 1;
  min-width: 200px;
}

.operations-right {
  flex-shrink: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.receipt-items-section {
  margin-top: 20px;
}

.receipt-items-section h3 {
  margin-bottom: 10px;
}

.total-amount {
  text-align: right;
  margin-top: 10px;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.mobile-table {
  font-size: 14px;
}

@media (max-width: 768px) {
  .purchase-receipts-container {
    padding: 10px;
  }
  
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left {
    margin-bottom: 10px;
  }
  
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 10px;
  }
}
</style>
