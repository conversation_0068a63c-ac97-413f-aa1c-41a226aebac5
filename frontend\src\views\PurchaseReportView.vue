<template>
  <div class="purchase-report-view">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <div class="header-info">
          <h1>采购报表</h1>
          <p>采购汇总分析和供应商统计</p>
        </div>
      </div>
    </div>

    <!-- 日期选择 -->
    <el-card class="date-card">
      <el-form :model="queryForm" inline>
        <el-form-item label="报表周期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchData">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 汇总统计 -->
    <el-card class="summary-card" v-if="reportData">
      <template #header>
        <span>采购汇总</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ reportData.total_orders }}</div>
            <div class="stat-label">采购订单数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">¥{{ formatNumber(reportData.total_amount) }}</div>
            <div class="stat-label">采购总金额</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatNumber(reportData.total_quantity) }}</div>
            <div class="stat-label">采购总数量</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ reportData.supplier_count }}</div>
            <div class="stat-label">供应商数量</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 图表分析 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>采购金额分布</span>
          </template>
          <EChart :option="amountPieOption" height="350px" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <span>采购数量排行</span>
          </template>
          <EChart :option="quantityBarOption" height="350px" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 明细表格 -->
    <el-card class="table-card">
      <template #header>
        <span>采购明细</span>
      </template>
      
      <el-table :data="reportData?.items || []" style="width: 100%">
        <el-table-column prop="material_code" label="物料编码" width="120" />
        <el-table-column prop="material_name" label="物料名称" width="200" />
        <el-table-column prop="quantity" label="采购数量" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.quantity) }}
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="amount" label="采购金额" width="150">
          <template #default="{ row }">
            ¥{{ formatNumber(row.amount) }}
          </template>
        </el-table-column>
        <el-table-column label="平均单价" width="120">
          <template #default="{ row }">
            ¥{{ (row.amount / row.quantity).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="占比" width="100">
          <template #default="{ row }">
            {{ ((row.amount / (reportData?.total_amount || 1)) * 100).toFixed(1) }}%
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Search } from '@element-plus/icons-vue'
import EChart from '@/components/EChart.vue'
import { reportsApi, type PurchaseSummaryReport } from '@/api/reports'

const route = useRoute()
const router = useRouter()

// 查询表单
const queryForm = reactive({
  start_date: '',
  end_date: ''
})

const dateRange = ref<[string, string]>([])
const reportData = ref<PurchaseSummaryReport | null>(null)

// 采购金额分布饼图
const amountPieOption = computed(() => {
  if (!reportData.value?.items) return {}
  
  const data = reportData.value.items.slice(0, 10).map(item => ({
    name: item.material_name,
    value: item.amount
  }))
  
  return {
    title: {
      text: '采购金额分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '采购金额',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})

// 采购数量排行柱状图
const quantityBarOption = computed(() => {
  if (!reportData.value?.items) return {}
  
  const sortedItems = [...reportData.value.items]
    .sort((a, b) => b.quantity - a.quantity)
    .slice(0, 10)
  
  const names = sortedItems.map(item => item.material_name)
  const quantities = sortedItems.map(item => item.quantity)
  
  return {
    title: {
      text: '采购数量排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: names,
      axisLabel: {
        interval: 0,
        formatter: (value: string) => {
          return value.length > 8 ? value.substring(0, 8) + '...' : value
        }
      }
    },
    series: [
      {
        name: '采购数量',
        type: 'bar',
        data: quantities,
        itemStyle: {
          color: '#409eff'
        }
      }
    ]
  }
})

// 获取数据
const fetchData = async () => {
  if (!queryForm.start_date || !queryForm.end_date) {
    ElMessage.warning('请选择查询日期范围')
    return
  }
  
  try {
    const res = await reportsApi.getPurchaseSummaryReport({
      start_date: queryForm.start_date,
      end_date: queryForm.end_date
    })
    reportData.value = res.data.data
  } catch (error) {
    console.error('获取采购报表失败:', error)
    ElMessage.error('获取采购报表失败')
  }
}

// 处理日期变化
const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    queryForm.start_date = dates[0]
    queryForm.end_date = dates[1]
    fetchData()
  }
}

// 返回
const goBack = () => {
  router.back()
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { maximumFractionDigits: 2 })
}

// 初始化
onMounted(() => {
  // 从URL参数获取日期范围
  const startDate = route.query.start_date as string
  const endDate = route.query.end_date as string
  
  if (startDate && endDate) {
    queryForm.start_date = startDate
    queryForm.end_date = endDate
    dateRange.value = [startDate, endDate]
    fetchData()
  } else {
    // 默认当月
    const now = new Date()
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)
    
    queryForm.start_date = firstDay.toISOString().split('T')[0]
    queryForm.end_date = lastDay.toISOString().split('T')[0]
    dateRange.value = [queryForm.start_date, queryForm.end_date]
    fetchData()
  }
})
</script>

<style scoped>
.purchase-report-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-info h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.date-card {
  margin-bottom: 24px;
}

.summary-card {
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 24px;
}

.table-card {
  margin-bottom: 24px;
}
</style>
