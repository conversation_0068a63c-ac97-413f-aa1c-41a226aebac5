<template>
  <div class="reports-view">
    <div class="page-header">
      <h1>报表中心</h1>
      <p>查看各类业务报表和数据分析</p>
    </div>

    <!-- 日期选择 -->
    <el-card class="date-card">
      <el-form :model="dateForm" inline>
        <el-form-item label="报表周期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="refreshReports">
            <el-icon><Refresh /></el-icon>
            刷新报表
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 报表导航 -->
    <div class="reports-nav">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="nav-card" @click="goToReport('purchase')">
            <div class="nav-content">
              <el-icon class="nav-icon"><ShoppingBag /></el-icon>
              <h3>采购报表</h3>
              <p>采购汇总、供应商分析</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="nav-card" @click="goToReport('sales')">
            <div class="nav-content">
              <el-icon class="nav-icon"><Goods /></el-icon>
              <h3>销售报表</h3>
              <p>销售汇总、客户分析</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="nav-card" @click="goToReport('inventory')">
            <div class="nav-content">
              <el-icon class="nav-icon"><Box /></el-icon>
              <h3>库存报表</h3>
              <p>库存变动、进销存分析</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="nav-card" @click="goToReport('production')">
            <div class="nav-content">
              <el-icon class="nav-icon"><Setting /></el-icon>
              <h3>生产报表</h3>
              <p>生产成本、效率分析</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 财务概览 -->
    <el-card class="financial-overview">
      <template #header>
        <span>财务概览</span>
      </template>
      
      <div v-if="financialData" class="financial-content">
        <el-row :gutter="20" class="financial-stats">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">¥{{ formatNumber(financialData.sales_amount) }}</div>
              <div class="stat-label">销售收入</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">¥{{ formatNumber(financialData.purchase_amount) }}</div>
              <div class="stat-label">采购支出</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">¥{{ formatNumber(financialData.gross_profit) }}</div>
              <div class="stat-label">毛利润</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ financialData.profit_margin.toFixed(1) }}%</div>
              <div class="stat-label">利润率</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="chart-container">
          <EChart :option="monthlyTrendOption" height="300px" />
        </div>
      </div>
      
      <div v-else class="loading-placeholder">
        <el-skeleton :rows="4" animated />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, ShoppingBag, Goods, Box, Setting } from '@element-plus/icons-vue'
import EChart from '@/components/EChart.vue'
import { reportsApi, type FinancialSummaryReport } from '@/api/reports'

const router = useRouter()

// 日期范围
const dateRange = ref<[string, string]>([])
const dateForm = reactive({
  start_date: '',
  end_date: ''
})

// 财务数据
const financialData = ref<FinancialSummaryReport | null>(null)

// 初始化日期范围（默认当月）
const initDateRange = () => {
  const now = new Date()
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0)
  
  const startDate = firstDay.toISOString().split('T')[0]
  const endDate = lastDay.toISOString().split('T')[0]
  
  dateRange.value = [startDate, endDate]
  dateForm.start_date = startDate
  dateForm.end_date = endDate
}

// 月度趋势图表配置
const monthlyTrendOption = computed(() => {
  if (!financialData.value?.monthly_data) {
    return {}
  }
  
  const months = financialData.value.monthly_data.map(item => item.month)
  const salesData = financialData.value.monthly_data.map(item => item.sales_amount)
  const purchaseData = financialData.value.monthly_data.map(item => item.purchase_amount)
  const profitData = financialData.value.monthly_data.map(item => item.profit)
  
  return {
    title: {
      text: '月度收支趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ¥${formatNumber(param.value)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['销售收入', '采购支出', '利润'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => `¥${formatNumber(value)}`
      }
    },
    series: [
      {
        name: '销售收入',
        type: 'line',
        data: salesData,
        smooth: true,
        itemStyle: { color: '#67c23a' }
      },
      {
        name: '采购支出',
        type: 'line',
        data: purchaseData,
        smooth: true,
        itemStyle: { color: '#e6a23c' }
      },
      {
        name: '利润',
        type: 'line',
        data: profitData,
        smooth: true,
        itemStyle: { color: '#409eff' }
      }
    ]
  }
})

// 处理日期变化
const handleDateChange = (dates: [string, string] | null) => {
  if (dates) {
    dateForm.start_date = dates[0]
    dateForm.end_date = dates[1]
    fetchFinancialData()
  }
}

// 获取财务数据
const fetchFinancialData = async () => {
  try {
    const res = await reportsApi.getFinancialSummaryReport({
      start_date: dateForm.start_date,
      end_date: dateForm.end_date
    })
    financialData.value = res.data.data
  } catch (error) {
    console.error('获取财务数据失败:', error)
    ElMessage.error('获取财务数据失败')
  }
}

// 刷新报表
const refreshReports = () => {
  fetchFinancialData()
  ElMessage.success('报表数据已刷新')
}

// 跳转到具体报表
const goToReport = (type: string) => {
  const routes = {
    purchase: '/reports/purchase',
    sales: '/reports/sales',
    inventory: '/reports/inventory',
    production: '/reports/production'
  }
  
  const route = routes[type as keyof typeof routes]
  if (route) {
    router.push(`${route}?start_date=${dateForm.start_date}&end_date=${dateForm.end_date}`)
  }
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { maximumFractionDigits: 2 })
}

onMounted(() => {
  initDateRange()
  fetchFinancialData()
})
</script>

<style scoped>
.reports-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.date-card {
  margin-bottom: 24px;
}

.reports-nav {
  margin-bottom: 24px;
}

.nav-card {
  cursor: pointer;
  transition: all 0.3s;
}

.nav-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-content {
  text-align: center;
  padding: 20px;
}

.nav-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 16px;
}

.nav-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.nav-content p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.financial-overview {
  margin-bottom: 24px;
}

.financial-content {
  padding: 20px 0;
}

.financial-stats {
  margin-bottom: 30px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.chart-container {
  margin-top: 20px;
}

.loading-placeholder {
  padding: 40px;
}
</style>
