<template>
  <div class="sales-deliveries-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>销售出库管理</h1>
    </div>

    <!-- 操作栏 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索出库单号或客户"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增出库单
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="salesDeliveries"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column prop="delivery_no" label="出库单号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="sales_order_no" label="销售订单号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="customer_name" label="客户" :min-width="isMobile ? 100 : 120" />
        <el-table-column v-if="!isMobile" prop="delivery_date" label="出库日期" min-width="100" />
        <el-table-column prop="total_amount" label="总金额" :min-width="isMobile ? 90 : 100">
          <template #default="{ row }">
            ¥{{ row.total_amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" :min-width="isMobile ? 80 : 100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'confirmed' ? 'success' : 'warning'">
              {{ row.status === 'confirmed' ? '已确认' : '草稿' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" :min-width="isMobile ? 100 : 120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewDelivery(row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建出库单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增销售出库单"
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="deliveryFormRef"
        :model="deliveryForm"
        :rules="deliveryRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="销售订单" prop="sales_order_id">
              <el-select 
                v-model="deliveryForm.sales_order_id" 
                placeholder="请选择销售订单" 
                style="width: 100%"
                @change="onSalesOrderChange"
              >
                <el-option
                  v-for="order in approvedOrders"
                  :key="order.id"
                  :label="`${order.order_no} - ${order.customer_name}`"
                  :value="order.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出库日期" prop="delivery_date">
              <el-date-picker
                v-model="deliveryForm.delivery_date"
                type="date"
                placeholder="选择出库日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="deliveryForm.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>

      <!-- 出库明细 -->
      <div v-if="selectedOrderItems.length > 0" class="delivery-items-section">
        <h3>出库明细</h3>
        <el-table :data="deliveryForm.items" style="width: 100%">
          <el-table-column prop="product_name" label="成品" min-width="150" />
          <el-table-column prop="order_quantity" label="订单数量" width="100" />
          <el-table-column label="出库数量" width="120">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.quantity" 
                :min="0" 
                :max="row.order_quantity"
                :precision="2" 
                style="width: 100%"
                @change="calculateItemTotal(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="单价" width="120">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.unit_price" 
                :min="0" 
                :precision="2" 
                style="width: 100%"
                @change="calculateItemTotal(row, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="小计" width="120">
            <template #default="{ row }">
              ¥{{ (row.quantity * row.unit_price || 0).toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
        
        <div class="total-amount">
          <strong>总金额: ¥{{ totalAmount.toFixed(2) }}</strong>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitDelivery">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 出库单详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="销售出库单详情" width="70%">
      <div v-if="currentDelivery">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="出库单号">{{ currentDelivery.delivery_no }}</el-descriptions-item>
          <el-descriptions-item label="销售订单号">{{ currentDelivery.sales_order_no }}</el-descriptions-item>
          <el-descriptions-item label="客户">{{ currentDelivery.customer_name }}</el-descriptions-item>
          <el-descriptions-item label="出库日期">{{ currentDelivery.delivery_date }}</el-descriptions-item>
          <el-descriptions-item label="总金额">¥{{ currentDelivery.total_amount.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentDelivery.status === 'confirmed' ? 'success' : 'warning'">
              {{ currentDelivery.status === 'confirmed' ? '已确认' : '草稿' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentDelivery.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px;">出库明细</h3>
        <el-table :data="currentDelivery.items" style="width: 100%">
          <el-table-column prop="product_code" label="成品编码" />
          <el-table-column prop="product_name" label="成品名称" />
          <el-table-column prop="quantity" label="出库数量" />
          <el-table-column prop="unit_price" label="单价">
            <template #default="{ row }">
              ¥{{ row.unit_price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="total_price" label="小计">
            <template #default="{ row }">
              ¥{{ row.total_price.toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { salesDeliveryApi, type SalesDelivery, type SalesDeliveryForm } from '@/api/salesDeliveries'
import { salesOrderApi, type SalesOrder } from '@/api/salesOrders'
import { useWindowSize } from '@/composables/useWindowSize'

const { isMobile } = useWindowSize()

// 响应式数据
const salesDeliveries = ref<SalesDelivery[]>([])
const approvedOrders = ref<SalesOrder[]>([])
const selectedOrderItems = ref<any[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentDelivery = ref<SalesDelivery | null>(null)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单数据
const deliveryFormRef = ref<FormInstance>()
const deliveryForm = reactive<SalesDeliveryForm>({
  sales_order_id: 0,
  delivery_date: '',
  remark: '',
  items: []
})

// 表单验证规则
const deliveryRules: FormRules = {
  sales_order_id: [{ required: true, message: '请选择销售订单', trigger: 'change' }],
  delivery_date: [{ required: true, message: '请选择出库日期', trigger: 'change' }]
}

// 计算总金额
const totalAmount = computed(() => {
  return deliveryForm.items.reduce((sum, item) => sum + (item.quantity * item.unit_price || 0), 0)
})

// 获取数据的方法
async function fetchSalesDeliveries() {
  loading.value = true
  try {
    const response = await salesDeliveryApi.getSalesDeliveries({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    })
    
    if (response.success && response.data) {
      salesDeliveries.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取销售出库单列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取销售出库单列表失败')
  } finally {
    loading.value = false
  }
}

async function fetchApprovedOrders() {
  try {
    const response = await salesOrderApi.getSalesOrders({ 
      limit: 1000, 
      status: 'approved' 
    })
    if (response.success && response.data) {
      approvedOrders.value = response.data.data
    }
  } catch (error) {
    console.error('获取已审核销售订单失败:', error)
  }
}

// 搜索和分页
function handleSearch() {
  currentPage.value = 1
  fetchSalesDeliveries()
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchSalesDeliveries()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchSalesDeliveries()
}

// 出库单操作
async function viewDelivery(delivery: SalesDelivery) {
  try {
    const response = await salesDeliveryApi.getSalesDelivery(delivery.id)
    if (response.success && response.data) {
      currentDelivery.value = response.data
      showDetailDialog.value = true
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取出库单详情失败')
  }
}

// 销售订单变化处理
async function onSalesOrderChange() {
  if (!deliveryForm.sales_order_id) {
    selectedOrderItems.value = []
    deliveryForm.items = []
    return
  }
  
  try {
    const response = await salesOrderApi.getSalesOrder(deliveryForm.sales_order_id)
    if (response.success && response.data) {
      selectedOrderItems.value = response.data.items || []
      deliveryForm.items = selectedOrderItems.value.map(item => ({
        product_id: item.product_id,
        product_name: item.product_name,
        order_quantity: item.quantity,
        quantity: item.quantity,
        unit_price: item.unit_price
      }))
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取销售订单详情失败')
  }
}

function calculateItemTotal(item: any, index: number) {
  // 计算会自动通过computed属性更新总金额
}

// 对话框操作
function cancelDialog() {
  showCreateDialog.value = false
  deliveryFormRef.value?.resetFields()
  Object.assign(deliveryForm, {
    sales_order_id: 0,
    delivery_date: '',
    remark: '',
    items: []
  })
  selectedOrderItems.value = []
}

async function submitDelivery() {
  if (!deliveryFormRef.value) return
  
  const valid = await deliveryFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  if (deliveryForm.items.length === 0) {
    ElMessage.error('请选择销售订单')
    return
  }
  
  submitLoading.value = true
  
  try {
    const response = await salesDeliveryApi.createSalesDelivery(deliveryForm)
    
    if (response.success) {
      ElMessage.success('销售出库单创建成功，库存已更新')
      showCreateDialog.value = false
      fetchSalesDeliveries()
      cancelDialog()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchSalesDeliveries()
  fetchApprovedOrders()
})
</script>

<style scoped>
.sales-deliveries-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.operations-left {
  flex: 1;
  min-width: 200px;
}

.operations-right {
  flex-shrink: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.delivery-items-section {
  margin-top: 20px;
}

.delivery-items-section h3 {
  margin-bottom: 10px;
}

.total-amount {
  text-align: right;
  margin-top: 10px;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.mobile-table {
  font-size: 14px;
}

@media (max-width: 768px) {
  .sales-deliveries-container {
    padding: 10px;
  }
  
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left {
    margin-bottom: 10px;
  }
  
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 10px;
  }
}
</style>
