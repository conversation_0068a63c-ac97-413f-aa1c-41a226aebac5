<template>
  <div class="stocktaking-detail-view">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <div class="header-info">
          <h1>{{ taskDetail?.task.title }}</h1>
          <p>任务编号：{{ taskDetail?.task.task_no }}</p>
        </div>
      </div>
      <div class="header-right">
        <el-tag :type="getStatusColor(taskDetail?.task.status)">
          {{ getStatusText(taskDetail?.task.status) }}
        </el-tag>
      </div>
    </div>

    <!-- 任务信息 -->
    <el-card class="task-info-card" v-if="taskDetail">
      <template #header>
        <span>任务信息</span>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="任务标题">{{ taskDetail.task.title }}</el-descriptions-item>
        <el-descriptions-item label="盘点日期">{{ formatDate(taskDetail.task.task_date) }}</el-descriptions-item>
        <el-descriptions-item label="任务状态">
          <el-tag :type="getStatusColor(taskDetail.task.status)">
            {{ getStatusText(taskDetail.task.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建人">{{ taskDetail.task.created_by_name }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(taskDetail.task.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">
          {{ taskDetail.task.completed_at ? formatDateTime(taskDetail.task.completed_at) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="任务描述" :span="3">
          {{ taskDetail.task.description || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 盘点明细 -->
    <el-card class="items-card">
      <template #header>
        <div class="card-header">
          <span>盘点明细</span>
          <div class="header-actions" v-if="taskDetail?.task.status === 'in_progress'">
            <el-button type="success" @click="batchAdjust" :disabled="selectedItems.length === 0">
              批量调整库存
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        :data="taskDetail?.items || []"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="taskDetail?.task.status === 'in_progress'"
          type="selection"
          width="55"
          :selectable="(row) => row.status === 'counted' && row.difference_quantity !== 0"
        />
        <el-table-column prop="item_code" label="编码" width="120" />
        <el-table-column prop="item_name" label="名称" width="200" />
        <el-table-column prop="item_type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.item_type === 'material' ? 'info' : 'success'">
              {{ row.item_type === 'material' ? '原材料' : '成品' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="system_quantity" label="系统库存" width="120" />
        <el-table-column prop="actual_quantity" label="实际库存" width="120">
          <template #default="{ row }">
            <el-input-number
              v-if="taskDetail?.task.status === 'in_progress' && row.status === 'pending'"
              v-model="row.actual_quantity"
              :min="0"
              :precision="2"
              size="small"
              @change="updateActualQuantity(row)"
            />
            <span v-else>{{ row.actual_quantity ?? '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="difference_quantity" label="差异数量" width="120">
          <template #default="{ row }">
            <span v-if="row.difference_quantity !== undefined" :class="getDifferenceClass(row.difference_quantity)">
              {{ row.difference_quantity > 0 ? '+' : '' }}{{ row.difference_quantity }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="item_unit" label="单位" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getItemStatusColor(row.status)">
              {{ getItemStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="150">
          <template #default="{ row }">
            <el-input
              v-if="taskDetail?.task.status === 'in_progress' && (row.status === 'pending' || row.status === 'counted')"
              v-model="row.remark"
              placeholder="请输入备注"
              size="small"
              @blur="updateRemark(row)"
            />
            <span v-else>{{ row.remark || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="counted_by_name" label="盘点人" width="100" />
        <el-table-column prop="counted_at" label="盘点时间" width="150">
          <template #default="{ row }">
            {{ row.counted_at ? formatDateTime(row.counted_at) : '-' }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="stats-row" v-if="taskDetail">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ totalItems }}</div>
            <div class="stats-label">总项目数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ countedItems }}</div>
            <div class="stats-label">已盘点</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ differenceItems }}</div>
            <div class="stats-label">有差异</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-value">{{ adjustedItems }}</div>
            <div class="stats-label">已调整</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { stocktakingApi, type StocktakingTaskDetail, type StocktakingItem } from '@/api/stocktaking'

const route = useRoute()
const router = useRouter()

const taskDetail = ref<StocktakingTaskDetail | null>(null)
const loading = ref(false)
const selectedItems = ref<StocktakingItem[]>([])

// 统计数据
const totalItems = computed(() => taskDetail.value?.items.length || 0)
const countedItems = computed(() => taskDetail.value?.items.filter(item => item.status !== 'pending').length || 0)
const differenceItems = computed(() => taskDetail.value?.items.filter(item => item.difference_quantity !== 0).length || 0)
const adjustedItems = computed(() => taskDetail.value?.items.filter(item => item.status === 'adjusted').length || 0)

// 获取任务详情
const fetchTaskDetail = async () => {
  loading.value = true
  try {
    const taskId = Number(route.params.id)
    const res = await stocktakingApi.getStocktakingTask(taskId)
    taskDetail.value = res.data
  } catch (error) {
    console.error('获取盘点任务详情失败:', error)
    ElMessage.error('获取盘点任务详情失败')
  } finally {
    loading.value = false
  }
}

// 返回
const goBack = () => {
  router.back()
}

// 更新实际库存数量
const updateActualQuantity = async (item: StocktakingItem) => {
  if (item.actual_quantity === undefined || item.actual_quantity === null) return
  
  try {
    await stocktakingApi.updateStocktakingItem(item.id, {
      actual_quantity: item.actual_quantity
    })
    
    // 重新获取数据以更新差异数量和状态
    await fetchTaskDetail()
    
  } catch (error) {
    console.error('更新实际库存失败:', error)
    ElMessage.error('更新实际库存失败')
  }
}

// 更新备注
const updateRemark = async (item: StocktakingItem) => {
  try {
    await stocktakingApi.updateStocktakingItem(item.id, {
      remark: item.remark
    })
  } catch (error) {
    console.error('更新备注失败:', error)
    ElMessage.error('更新备注失败')
  }
}

// 选择变化
const handleSelectionChange = (selection: StocktakingItem[]) => {
  selectedItems.value = selection
}

// 批量调整库存
const batchAdjust = async () => {
  try {
    const itemIds = selectedItems.value.map(item => item.id)
    
    await ElMessageBox.confirm(
      `确定要调整选中的 ${selectedItems.value.length} 个库存项目吗？调整后将无法撤销。`,
      '确认调整',
      { type: 'warning' }
    )
    
    await stocktakingApi.adjustInventory(taskDetail.value!.task.id, itemIds)
    
    ElMessage.success('库存调整成功')
    await fetchTaskDetail()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('库存调整失败:', error)
      ElMessage.error('库存调整失败')
    }
  }
}

// 工具函数
const getStatusColor = (status?: string) => {
  switch (status) {
    case 'draft': return 'info'
    case 'in_progress': return 'warning'
    case 'completed': return 'success'
    case 'cancelled': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status?: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return status
  }
}

const getItemStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'info'
    case 'counted': return 'warning'
    case 'adjusted': return 'success'
    default: return 'info'
  }
}

const getItemStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待盘点'
    case 'counted': return '已盘点'
    case 'adjusted': return '已调整'
    default: return status
  }
}

const getDifferenceClass = (difference: number) => {
  if (difference > 0) return 'text-success'
  if (difference < 0) return 'text-danger'
  return 'text-info'
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchTaskDetail()
})
</script>

<style scoped>
.stocktaking-detail-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-info h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.task-info-card {
  margin-bottom: 24px;
}

.items-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  padding: 20px;
}

.stats-value {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}
</style>
