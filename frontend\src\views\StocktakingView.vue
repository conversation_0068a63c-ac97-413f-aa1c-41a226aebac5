<template>
  <div class="stocktaking-view">
    <div class="page-header">
      <h1>库存盘点</h1>
      <p>管理库存盘点任务，确保库存数据准确性</p>
    </div>

    <!-- 搜索和操作 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入任务编号或标题"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新建盘点
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 盘点任务列表 -->
    <el-card class="table-card">
      <DataTable
        :data="tableData"
        :columns="columns"
        :loading="loading"
        :total="pagination.total"
        @page-change="handlePageChange"
      >
        <template #status="{ row }">
          <el-tag :type="getStatusColor(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
        
        <template #task_date="{ row }">
          {{ formatDate(row.task_date) }}
        </template>
        
        <template #created_at="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
        
        <template #actions="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="viewTask(row)"
          >
            查看详情
          </el-button>
          <el-button
            v-if="row.status === 'draft'"
            type="warning"
            size="small"
            @click="startTask(row)"
          >
            开始盘点
          </el-button>
          <el-button
            v-if="row.status === 'in_progress'"
            type="success"
            size="small"
            @click="completeTask(row)"
          >
            完成盘点
          </el-button>
        </template>
      </DataTable>
    </el-card>

    <!-- 创建盘点任务对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新建盘点任务"
      width="600px"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="任务标题" prop="title">
          <el-input
            v-model="createForm.title"
            placeholder="请输入任务标题"
          />
        </el-form-item>
        <el-form-item label="盘点日期" prop="task_date">
          <el-date-picker
            v-model="createForm.task_date"
            type="date"
            placeholder="请选择盘点日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="盘点范围" prop="item_types">
          <el-checkbox-group v-model="createForm.item_types">
            <el-checkbox label="material">原材料</el-checkbox>
            <el-checkbox label="product">成品</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreate" :loading="createLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import DataTable from '@/components/DataTable.vue'
import { stocktakingApi, type StocktakingTask, type StocktakingTaskCreateInput } from '@/api/stocktaking'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  search: '',
  status: ''
})

// 表格数据
const tableData = ref<StocktakingTask[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 表格列配置
const columns = [
  { prop: 'task_no', label: '任务编号', width: 150 },
  { prop: 'title', label: '任务标题', width: 200 },
  { prop: 'status', label: '状态', width: 100, slot: 'status' },
  { prop: 'task_date', label: '盘点日期', width: 120, slot: 'task_date' },
  { prop: 'created_by_name', label: '创建人', width: 100 },
  { prop: 'created_at', label: '创建时间', width: 180, slot: 'created_at' },
  { prop: 'description', label: '描述', minWidth: 150 },
  { prop: 'actions', label: '操作', width: 200, slot: 'actions', fixed: 'right' }
]

// 创建对话框
const createDialogVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref<FormInstance>()

const createForm = reactive<StocktakingTaskCreateInput & { task_date: Date | null }>({
  title: '',
  description: '',
  task_date: null,
  item_types: ['material', 'product']
})

const createRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' }
  ],
  task_date: [
    { required: true, message: '请选择盘点日期', trigger: 'change' }
  ],
  item_types: [
    { required: true, message: '请选择盘点范围', trigger: 'change' }
  ]
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      search: searchForm.search || undefined,
      status: searchForm.status || undefined
    }
    
    const res = await stocktakingApi.getStocktakingTasks(params)
    tableData.value = res.data.data
    pagination.total = res.data.total
  } catch (error) {
    console.error('获取盘点任务失败:', error)
    ElMessage.error('获取盘点任务失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.search = ''
  searchForm.status = ''
  pagination.page = 1
  fetchData()
}

// 分页处理
const handlePageChange = (page: number, size?: number) => {
  pagination.page = page
  if (size !== undefined) {
    pagination.limit = size
  }
  fetchData()
}

// 显示创建对话框
const showCreateDialog = () => {
  createForm.title = ''
  createForm.description = ''
  createForm.task_date = null
  createForm.item_types = ['material', 'product']
  createDialogVisible.value = true
}

// 创建盘点任务
const handleCreate = async () => {
  if (!createFormRef.value) return
  
  try {
    await createFormRef.value.validate()
    
    createLoading.value = true
    
    const data: StocktakingTaskCreateInput = {
      title: createForm.title,
      description: createForm.description,
      task_date: createForm.task_date!.toISOString().split('T')[0],
      item_types: createForm.item_types
    }
    
    await stocktakingApi.createStocktakingTask(data)
    
    ElMessage.success('盘点任务创建成功')
    createDialogVisible.value = false
    fetchData()
    
  } catch (error) {
    console.error('创建盘点任务失败:', error)
    ElMessage.error('创建盘点任务失败')
  } finally {
    createLoading.value = false
  }
}

// 查看任务详情
const viewTask = (task: StocktakingTask) => {
  router.push(`/stocktaking/${task.id}`)
}

// 开始盘点
const startTask = async (task: StocktakingTask) => {
  try {
    await ElMessageBox.confirm('确定要开始此盘点任务吗？', '确认操作', {
      type: 'warning'
    })
    
    await stocktakingApi.updateStocktakingTask(task.id, { status: 'in_progress' })
    
    ElMessage.success('盘点任务已开始')
    fetchData()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始盘点任务失败:', error)
      ElMessage.error('开始盘点任务失败')
    }
  }
}

// 完成盘点
const completeTask = async (task: StocktakingTask) => {
  try {
    await ElMessageBox.confirm('确定要完成此盘点任务吗？完成后将无法再修改盘点数据。', '确认操作', {
      type: 'warning'
    })
    
    await stocktakingApi.updateStocktakingTask(task.id, { status: 'completed' })
    
    ElMessage.success('盘点任务已完成')
    fetchData()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成盘点任务失败:', error)
      ElMessage.error('完成盘点任务失败')
    }
  }
}

// 工具函数
const getStatusColor = (status: string) => {
  switch (status) {
    case 'draft': return 'info'
    case 'in_progress': return 'warning'
    case 'completed': return 'success'
    case 'cancelled': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'draft': return '草稿'
    case 'in_progress': return '进行中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    default: return status
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.stocktaking-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}
</style>
