<template>
  <div class="suppliers-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>供应商管理</h1>
    </div>

    <!-- 操作栏 - 左右分布 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索供应商编码、名称或联系人"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          新增供应商
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="suppliers"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column
          prop="code"
          label="编码"
          :min-width="isMobile ? 100 : 120"
          :width="isMobile ? 100 : undefined"
        />
        <el-table-column
          prop="name"
          label="名称"
          :min-width="isMobile ? 120 : 150"
          :width="isMobile ? 120 : undefined"
        />
        <el-table-column
          v-if="!isMobile"
          prop="contact_person"
          label="联系人"
          min-width="100"
        />
        <el-table-column
          v-if="!isMobile"
          prop="phone"
          label="电话"
          min-width="120"
        />
        <el-table-column
          v-if="!isMobile"
          prop="settlement_method"
          label="结算方式"
          min-width="100"
        />
        <el-table-column
          prop="status"
          label="状态"
          :min-width="isMobile ? 80 : 100"
          :width="isMobile ? 80 : undefined"
        >
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          :min-width="isMobile ? 120 : 150"
          :width="isMobile ? 120 : undefined"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editSupplier(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteSupplier(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingSupplier ? '编辑供应商' : '新增供应商'"
      width="600px"
    >
      <el-form
        ref="supplierFormRef"
        :model="supplierForm"
        :rules="supplierRules"
        label-width="100px"
      >
        <el-form-item label="编码" prop="code">
          <el-input v-model="supplierForm.code" />
        </el-form-item>
        
        <el-form-item label="名称" prop="name">
          <el-input v-model="supplierForm.name" />
        </el-form-item>
        
        <el-form-item label="联系人" prop="contact_person">
          <el-input v-model="supplierForm.contact_person" />
        </el-form-item>
        
        <el-form-item label="电话" prop="phone">
          <el-input v-model="supplierForm.phone" />
        </el-form-item>
        
        <el-form-item label="地址" prop="address">
          <el-input v-model="supplierForm.address" type="textarea" :rows="3" />
        </el-form-item>
        
        <el-form-item label="结算方式" prop="settlement_method">
          <el-select v-model="supplierForm.settlement_method" placeholder="请选择结算方式">
            <el-option label="现金" value="现金" />
            <el-option label="月结" value="月结" />
            <el-option label="季结" value="季结" />
            <el-option label="半年结" value="半年结" />
            <el-option label="年结" value="年结" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitSupplier">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { supplierApi, type Supplier, type SupplierForm } from '@/api/suppliers'
import { useWindowSize } from '@/composables/useWindowSize'

// 使用全局窗口大小管理器
const { isMobile, isTablet, isDesktop, isLargeScreen } = useWindowSize()

// 响应式数据
const suppliers = ref<Supplier[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showAddDialog = ref(false)
const editingSupplier = ref<Supplier | null>(null)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单引用和数据
const supplierFormRef = ref<FormInstance>()
const supplierForm = reactive<SupplierForm>({
  code: '',
  name: '',
  contact_person: '',
  phone: '',
  address: '',
  settlement_method: ''
})

// 表单验证规则
const supplierRules: FormRules = {
  code: [
    { required: true, message: '请输入供应商编码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' }
  ]
}

// 获取供应商列表
async function fetchSuppliers() {
  loading.value = true
  try {
    const response = await supplierApi.getSuppliers({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value
    })
    
    if (response.success && response.data) {
      suppliers.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取供应商列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取供应商列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
function handleSearch() {
  currentPage.value = 1
  fetchSuppliers()
}

// 分页处理
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchSuppliers()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchSuppliers()
}

// 编辑供应商
function editSupplier(supplier: Supplier) {
  editingSupplier.value = supplier
  Object.assign(supplierForm, {
    code: supplier.code,
    name: supplier.name,
    contact_person: supplier.contact_person || '',
    phone: supplier.phone || '',
    address: supplier.address || '',
    settlement_method: supplier.settlement_method || ''
  })
  showAddDialog.value = true
}

// 删除供应商
async function deleteSupplier(supplier: Supplier) {
  try {
    await ElMessageBox.confirm(
      `确定要删除供应商 "${supplier.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await supplierApi.deleteSupplier(supplier.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchSuppliers()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 取消对话框
function cancelDialog() {
  showAddDialog.value = false
  editingSupplier.value = null
  supplierFormRef.value?.resetFields()
  Object.assign(supplierForm, {
    code: '',
    name: '',
    contact_person: '',
    phone: '',
    address: '',
    settlement_method: ''
  })
}

// 提交供应商
async function submitSupplier() {
  if (!supplierFormRef.value) return
  
  const valid = await supplierFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  submitLoading.value = true
  
  try {
    let response
    if (editingSupplier.value) {
      response = await supplierApi.updateSupplier(editingSupplier.value.id, supplierForm)
    } else {
      response = await supplierApi.createSupplier(supplierForm)
    }
    
    if (response.success) {
      ElMessage.success(editingSupplier.value ? '更新成功' : '创建成功')
      showAddDialog.value = false
      fetchSuppliers()
      cancelDialog()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchSuppliers()
})
</script>

<style scoped>
.suppliers-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.operations-left {
  flex: 1;
  min-width: 200px;
}

.operations-right {
  flex-shrink: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mobile-table {
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .suppliers-container {
    padding: 10px;
  }
  
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left {
    margin-bottom: 10px;
  }
  
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 10px;
  }
}
</style>
